{"format_version": "1.21.50", "minecraft:entity": {"description": {"identifier": "minecraft:wandering_trader", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"managed": {"minecraft:managed_wandering_trader": {}}, "despawning": {"minecraft:type_family": {"family": ["wandering_trader", "wandering_trader_despawning", "mob"]}}, "minecraft:scared": {"minecraft:angry": {"duration": 5, "broadcast_anger": true, "broadcast_range": 10, "broadcast_targets": ["llama", "trader_llama"], "broadcast_filters": {"test": "is_leashed_to", "subject": "other", "value": true}, "calm_event": {"event": "minecraft:become_calm", "target": "self"}}}}, "events": {"minecraft:scheduled": {"add": {"component_groups": ["managed"]}}, "minecraft:start_despawn": {"add": {"component_groups": ["despawning"]}}, "minecraft:become_scared": {"add": {"component_groups": ["minecraft:scared"]}}, "minecraft:become_calm": {"remove": {"component_groups": ["minecraft:scared"]}}}, "components": {"minecraft:type_family": {"family": ["wandering_trader", "mob"]}, "minecraft:is_hidden_when_invisible": {}, "minecraft:timer": {"looping": false, "random_time_choices": [{"weight": 50, "value": 2400}, {"weight": 50, "value": 3600}], "time_down_event": {"event": "minecraft:start_despawn", "target": "self"}}, "minecraft:spawn_entity": {"entities": [{"min_wait_time": 0, "max_wait_time": 0, "spawn_entity": "trader_llama", "spawn_event": "minecraft:from_wandering_trader", "single_use": true, "num_to_spawn": 2, "should_leash": true}]}, "minecraft:trade_table": {"convert_trades_economy": false, "display_name": "entity.wandering_trader.name", "new_screen": true, "persist_trades": false, "table": "trading/wandering_trader_trades.json"}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:behavior.ranged_attack": {"priority": 4, "swing": true, "speed_multiplier": 0.6, "attack_interval_min": 3, "attack_interval_max": 3, "attack_radius": 16}, "minecraft:shooter": {"power": 1, "def": "minecraft:splash_potion", "aux_val": 24, "sound": "throw", "projectiles": [{"def": "minecraft:splash_potion", "aux_val": 17, "filters": {"all_of": [{"test": "target_distance", "subject": "self", "value": 6, "operator": ">="}, {"none_of": [{"test": "has_mob_effect", "subject": "other", "value": "slowness"}]}]}, "chance": 0.25}, {"def": "minecraft:splash_potion", "aux_val": 25, "filters": {"all_of": [{"test": "actor_health", "subject": "other", "value": 8, "operator": ">="}, {"none_of": [{"test": "has_mob_effect", "subject": "other", "value": "poison"}]}, {"test": "is_family", "subject": "other", "value": "player"}]}}, {"def": "minecraft:splash_potion", "aux_val": 34, "filters": {"all_of": [{"test": "target_distance", "subject": "self", "value": 3, "operator": "<="}, {"none_of": [{"test": "has_mob_effect", "subject": "other", "value": "weakness"}]}]}, "chance": 0.25}], "magic": true}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:movement": {"value": 0.25}, "minecraft:navigation.walk": {"can_path_over_water": true, "can_pass_doors": true, "can_open_doors": false, "avoid_water": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.hurt_by_target": {"priority": 5, "entity_types": {"must_see": true, "must_see_forget_duration": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}}}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:despawn": {"remove_child_entities": true, "filters": {"all_of": [{"any_of": [{"test": "is_family", "subject": "self", "value": "wandering_trader_despawning"}, {"test": "has_trade_supply", "subject": "self", "value": false}]}, {"test": "distance_to_nearest_player", "operator": ">", "value": 24}]}}, "minecraft:damage_sensor": {"triggers": [{"cause": "entity_attack", "deals_damage": "yes", "on_damage": {"event": "minecraft:become_scared"}}, {"cause": "projectile", "deals_damage": "yes", "on_damage": {"event": "minecraft:become_scared"}}, {"cause": "magic", "deals_damage": "yes", "on_damage": {"event": "minecraft:become_scared"}}, {"on_damage": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "turret"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}}, "deals_damage": "no"}]}, "minecraft:behavior.trade_with_player": {"priority": 1, "filters": {"all_of": [{"all_of": [{"test": "in_water", "value": false}]}, {"any_of": [{"test": "on_ground", "value": true}, {"test": "is_sleeping", "value": true}]}]}}, "minecraft:behavior.trade_interest": {"priority": 3, "within_radius": 6, "interest_time": 45, "remove_item_time": 1, "carried_item_switch_time": 2, "cooldown": 2}, "minecraft:behavior.look_at_trading_player": {"priority": 4}, "minecraft:behavior.panic": {"priority": 1, "speed_multiplier": 0.6}, "minecraft:behavior.drink_potion": {"priority": 1, "speed_modifier": -0.2, "potions": [{"id": 7, "chance": 0, "filters": {"all_of": [{"test": "is_visible", "subject": "self", "value": false}, {"any_of": [{"test": "is_avoiding_mobs", "subject": "self", "value": true}, {"all_of": [{"test": "has_component", "subject": "self", "value": "minecraft:angry"}, {"test": "is_family", "subject": "target", "operator": "!=", "value": "player"}]}]}]}}, {"id": 8, "chance": 0, "filters": {"all_of": [{"test": "is_visible", "subject": "self", "value": true}, {"any_of": [{"test": "is_avoiding_mobs", "subject": "self", "value": true}, {"test": "has_component", "subject": "self", "value": "minecraft:angry"}]}]}}, {"id": 21, "chance": 1, "filters": {"all_of": [{"test": "is_missing_health", "subject": "self", "value": true}]}}, {"id": 14, "chance": 1, "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "speed"}]}, {"test": "target_distance", "subject": "self", "value": 6, "operator": "<="}]}}, {"id": 19, "chance": 0.15, "filters": {"all_of": [{"test": "is_underwater", "subject": "self", "value": true}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "water_breathing"}]}]}}, {"id": 12, "chance": 0.15, "filters": {"all_of": [{"any_of": [{"test": "on_fire", "subject": "self", "value": true}, {"test": "on_hot_block", "subject": "self", "value": true}, {"test": "taking_fire_damage", "subject": "self", "value": true}]}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "fire_resistance"}]}]}}]}, "minecraft:behavior.drink_milk": {"priority": 5, "filters": {"all_of": [{"test": "is_daytime", "value": true}, {"test": "is_visible", "subject": "self", "value": false}, {"test": "is_avoiding_mobs", "subject": "self", "value": false}]}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 2, "must_see": true, "must_reach": true, "persist_time": 0, "reselect_targets": true, "within_radius": 12, "reevaluate_description": true, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"none_of": [{"all_of": [{"any_of": [{"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_illager"}]}, {"test": "has_mob_effect", "subject": "other", "value": "weakness"}, {"test": "has_component", "subject": "other", "value": "minecraft:transformation"}]}]}]}, "max_dist": 12}]}, "minecraft:behavior.avoid_mob_type": {"priority": 4, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "zombie"}]}, "max_dist": 8, "walk_speed_multiplier": 0.6, "sprint_speed_multiplier": 0.6}]}, "minecraft:behavior.move_towards_home_restriction": {"priority": 6, "speed_multiplier": 0.6}, "minecraft:behavior.random_stroll": {"priority": 7}, "minecraft:behavior.look_at_player": {"priority": 8, "look_distance": 8, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 9}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:nameable": {}}}}