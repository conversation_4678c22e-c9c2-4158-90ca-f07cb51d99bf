{"format_version": "1.16.0", "minecraft:geometry": [{"description": {"identifier": "geometry.pyro_charger_item", "texture_width": 160, "texture_height": 160, "visible_bounds_width": 5, "visible_bounds_height": 3.5, "visible_bounds_offset": [0, 1.25, 0]}, "bones": [{"name": "root", "pivot": [0, -2, 0], "binding": "q.item_slot_to_bone_name(c.item_slot)"}, {"name": "bone3", "parent": "root", "pivot": [0.01923, 4.26923, -0.30673], "cubes": [{"origin": [-3, -2, -3], "size": [6, 6, 6], "uv": [96, 50]}]}, {"name": "bone", "parent": "root", "pivot": [0, 7, 0], "cubes": [{"origin": [-4.5, 4, -5], "size": [9, 8, 12], "uv": [80, 0]}, {"origin": [4.5, 2, -6], "size": [2, 4, 8], "uv": [40, 87]}, {"origin": [0, 2, -6], "size": [4.5, 2, 8], "uv": [46, 74]}, {"origin": [0, 2, 2.05], "size": [7, 2, 6.95], "uv": [107, 37]}, {"origin": [4.5, 4, 2], "size": [3, 6, 7], "uv": [0, 87]}, {"origin": [0.5, 4, 7], "size": [4, 6, 2], "uv": [90, 120]}, {"origin": [1.5, 5, 9], "size": [5, 4, 1], "uv": [56, 131]}, {"origin": [-6.5, 5, 9], "size": [5, 4, 1], "uv": [44, 131]}, {"origin": [-6.5, 2, -6], "size": [2, 4, 8], "uv": [20, 87]}, {"origin": [-6.5, 6, 0], "size": [2, 7, 2], "uv": [36, 131]}, {"origin": [-6.5, 6, -4], "size": [2, 7, 2], "uv": [28, 131]}, {"origin": [-7.5, 11, 2], "size": [3, 1, 3], "pivot": [-5.5, 12, 3], "rotation": [0, 0, -45], "uv": [72, 140]}, {"origin": [-7.5, 9.25, -8], "size": [3, 1, 3], "pivot": [-5.5, 12, 3], "rotation": [0, 0, -45], "uv": [60, 140]}, {"origin": [-4.46967, 8.84835, -8], "size": [1.25, 1, 3], "pivot": [-4.96967, 9.34835, -6.5], "rotation": [0, 0, 42.5], "uv": [8, 152]}, {"origin": [-7, 12, 2.5], "size": [2, 4, 2], "pivot": [-5.5, 12, 3], "rotation": [0, 0, -45], "uv": [32, 146]}, {"origin": [-7, 10, -7.5], "size": [2, 4, 2], "pivot": [-5.5, 12, 3], "rotation": [0, 0, -45], "uv": [24, 146]}, {"origin": [-7, 14, -7.5], "size": [2, 2, 10], "pivot": [-5.5, 12, 3], "rotation": [0, 0, -45], "uv": [72, 50]}, {"origin": [4.5, 9.25, -8], "size": [3, 1, 3], "pivot": [5.5, 12, 3], "rotation": [0, 0, 45], "uv": [48, 140]}, {"origin": [3.21967, 8.84835, -8], "size": [1.25, 1, 3], "pivot": [4.96967, 9.34835, -6.5], "rotation": [0, 0, -42.5], "uv": [0, 152]}, {"origin": [5, 14, -7.5], "size": [2, 2, 10], "pivot": [5.5, 12, 3], "rotation": [0, 0, 45], "uv": [48, 50]}, {"origin": [5, 10, -7.5], "size": [2, 4, 2], "pivot": [5.5, 12, 3], "rotation": [0, 0, 45], "uv": [16, 146]}, {"origin": [5, 12, 2.5], "size": [2, 4, 2], "pivot": [5.5, 12, 3], "rotation": [0, 0, 45], "uv": [8, 146]}, {"origin": [4.5, 11, 2], "size": [3, 1, 3], "pivot": [5.5, 12, 3], "rotation": [0, 0, 45], "uv": [36, 140]}, {"origin": [4.5, 5.75, -8], "size": [3, 1, 3], "pivot": [5.5, 4, 3], "rotation": [0, 0, -45], "uv": [24, 140]}, {"origin": [3.21967, 6.15165, -8], "size": [1.25, 1, 3], "pivot": [4.96967, 6.65165, -6.5], "rotation": [0, 0, 42.5], "uv": [94, 146]}, {"origin": [5, 0, -7.5], "size": [2, 2, 9.5], "pivot": [5.5, 4, 3], "rotation": [0, 0, -45], "uv": [112, 74]}, {"origin": [5, 2, -7.5], "size": [2, 4, 2], "pivot": [5.5, 4, 3], "rotation": [0, 0, -45], "uv": [0, 146]}, {"origin": [5, 0, 2], "size": [2, 1, 2.5], "pivot": [5.5, 4, 3], "rotation": [0, 0, -45], "uv": [40, 152]}, {"origin": [4.5, 1, 2], "size": [3, 1, 3], "pivot": [5.5, 4, 3], "rotation": [0, 0, -45], "uv": [12, 140]}, {"origin": [6.5, 2, 1.975], "size": [1, 1, 3.025], "pivot": [5.5, 4, 3], "rotation": [0, 0, -45], "uv": [86, 146]}, {"origin": [-7.5, 2, 1.975], "size": [1, 1, 3.025], "pivot": [-5.5, 4, 3], "rotation": [0, 0, 45], "uv": [78, 146]}, {"origin": [-7.5, 1, 2], "size": [3, 1, 3], "pivot": [-5.5, 4, 3], "rotation": [0, 0, 45], "uv": [0, 140]}, {"origin": [-7, 0, -7.5], "size": [2, 2, 9.5], "pivot": [-5.5, 4, 3], "rotation": [0, 0, 45], "uv": [90, 74]}, {"origin": [-7, 0, 2], "size": [2, 1, 2.5], "pivot": [-5.5, 4, 3], "rotation": [0, 0, 45], "uv": [32, 152]}, {"origin": [-7, 2, -7.5], "size": [2, 4, 2], "pivot": [-5.5, 4, 3], "rotation": [0, 0, 45], "uv": [116, 140]}, {"origin": [-7.5, 5.75, -8], "size": [3, 1, 3], "pivot": [-5.5, 4, 3], "rotation": [0, 0, 45], "uv": [98, 131]}, {"origin": [-4.46967, 6.15165, -8], "size": [1.25, 1, 3], "pivot": [-4.96967, 6.65165, -6.5], "rotation": [0, 0, -42.5], "uv": [68, 148]}, {"origin": [-2.5, 12, 5], "size": [2, 1, 2], "uv": [24, 152]}, {"origin": [-4.5, 12, 0], "size": [4, 1, 2], "uv": [84, 143]}, {"origin": [-4.5, 12, -4], "size": [3, 1, 2], "uv": [40, 149]}, {"origin": [-1.5, 12, -2], "size": [3, 4, 2], "uv": [88, 131]}, {"origin": [-1.5, 12, -8], "size": [3, 4, 2], "uv": [78, 131]}, {"origin": [-1.5, 14, -6], "size": [3, 2, 4], "uv": [76, 120]}, {"origin": [-1.5, 0, -8], "size": [3, 4, 2], "uv": [68, 131]}, {"origin": [-1.5, 0, -6], "size": [3, 2, 3], "uv": [16, 131]}, {"origin": [-2.5, 10, 7], "size": [2, 3, 1], "uv": [62, 152]}, {"origin": [0.5, 10, 7], "size": [2, 3, 1], "uv": [56, 152]}, {"origin": [0.5, 12, 5], "size": [2, 1, 2], "uv": [16, 152]}, {"origin": [2.5, 11.83395, 1.89798], "size": [2, 1, 5.1], "pivot": [3.5, 12.33395, 2.89798], "rotation": [-10, 0, 0], "uv": [62, 120]}, {"origin": [0.5, 11.83395, 1.89798], "size": [2, 1, 3.1], "pivot": [3.5, 12.33395, 2.89798], "rotation": [-10, 0, 0], "uv": [106, 140]}, {"origin": [-2.5, 11.83395, 1.89798], "size": [2, 1, 3.1], "pivot": [-3.5, 12.33395, 2.89798], "rotation": [-10, 0, 0], "uv": [96, 140]}, {"origin": [-4.5, 11.83395, 1.89798], "size": [2, 1, 5.1], "pivot": [-3.5, 12.33395, 2.89798], "rotation": [-10, 0, 0], "uv": [48, 120]}, {"origin": [3.94465, 10.37419, 1.95], "size": [3.25, 1.1, 5.025], "pivot": [4.94465, 10.97419, 6], "rotation": [0, 0, 40], "uv": [102, 125]}, {"origin": [-7.19465, 10.37419, 2], "size": [3.25, 1.1, 4.975], "pivot": [-4.94465, 10.97419, 6], "rotation": [0, 0, -40], "uv": [102, 120]}, {"origin": [4.5, 6, 0], "size": [2, 7, 2], "uv": [8, 131]}, {"origin": [0.5, 12, 0], "size": [4, 1, 2], "uv": [84, 140]}, {"origin": [1.5, 12, -4], "size": [3, 1, 2], "uv": [40, 146]}, {"origin": [4.5, 6, -4], "size": [2, 7, 2], "uv": [0, 131]}, {"origin": [-4.5, 2, -6], "size": [4.5, 2, 8], "uv": [22, 74]}, {"origin": [-7, 2, 2], "size": [7, 2, 7], "uv": [78, 36]}, {"origin": [-7.5, 4, 2], "size": [3, 6, 7], "uv": [70, 74]}, {"origin": [-4.5, 4, 7], "size": [4, 6, 2], "uv": [36, 120]}, {"origin": [3, 1, -4], "size": [4.85, 1, 7], "uv": [0, 109]}, {"origin": [-7.85, 1, -4], "size": [4.85, 1, 7], "uv": [110, 100]}]}, {"name": "pyro_tubes", "parent": "bone", "pivot": [0, 7.5, 0], "cubes": [{"origin": [-4.75, 6.25, 9.5], "size": [1.75, 1.5, 2.25], "uv": [74, 152]}, {"origin": [-9, 6.5, 10.5], "size": [4.25, 1, 1], "uv": [68, 146]}, {"origin": [3, 6.25, 9.5], "size": [1.75, 1.5, 2.25], "uv": [68, 152]}, {"origin": [4.75, 6.5, 10.5], "size": [4.25, 1, 1], "uv": [58, 150]}, {"origin": [-9, 6.5, -0.5], "size": [1, 1, 11], "uv": [24, 50]}, {"origin": [-9, 6.5, -1.5], "size": [4.5, 1, 1], "uv": [0, 0]}, {"origin": [8, 6.5, -0.5], "size": [1, 1, 11], "uv": [0, 50]}, {"origin": [4.5, 6.5, -1.5], "size": [4.5, 1, 1], "uv": [0, 2]}], "locators": {"sparks1": [-4, 7, 10], "sparks2": [-4, 7, 11], "sparks3": [-5, 7, 11], "sparks4": [-6, 7, 11], "sparks5": [-7, 7, 11], "sparks6": [-8, 7, 11], "sparks7": [-8.5, 7, 10], "sparks8": [-8.5, 7, 9], "sparks9": [-8.5, 7, 8], "sparks10": [-8.5, 7, 7], "sparks11": [-8.5, 7, 6], "sparks12": [-8.5, 7, 5], "sparks13": [-8.5, 7, 4], "sparks14": [-8.5, 7, 3], "sparks15": [-8.5, 7, 2], "sparks16": [-8.5, 7, 1], "sparks17": [-8.5, 7, 0], "sparks18": [-8, 7, -1], "sparks19": [-7, 7, -1], "sparks20": [-6, 7, -1], "sparks40": [4, 7, 10], "sparks39": [4, 7, 11], "sparks38": [5, 7, 11], "sparks37": [6, 7, 11], "sparks36": [7, 7, 11], "sparks35": [8, 7, 11], "sparks34": [8.5, 7, 10], "sparks33": [8.5, 7, 9], "sparks32": [8.5, 7, 8], "sparks31": [8.5, 7, 7], "sparks30": [8.5, 7, 6], "sparks29": [8.5, 7, 5], "sparks28": [8.5, 7, 4], "sparks27": [8.5, 7, 3], "sparks26": [8.5, 7, 2], "sparks25": [8.5, 7, 1], "sparks24": [8.5, 7, 0], "sparks23": [8, 7, -1], "sparks22": [7, 7, -1], "sparks21": [6, 7, -1]}}, {"name": "barrel", "parent": "root", "pivot": [8, 0, -13.5], "cubes": [{"origin": [-1.65685, 4, -13.025], "size": [3.31371, 1, 8.025], "pivot": [0, 8, -5.5], "rotation": [0, 0, 45], "uv": [88, 100]}, {"origin": [-1.65685, 4, -13], "size": [3.31371, 1, 8], "uv": [66, 100]}, {"origin": [-1.65685, 11, -13.025], "size": [3.31371, 1, 8.025], "pivot": [0, 8, -5.5], "rotation": [0, 0, 45], "uv": [44, 100]}, {"origin": [-1.65685, 11, -13], "size": [3.31371, 1, 8], "uv": [22, 100]}, {"origin": [3, 6.34315, -13.025], "size": [1, 3.31371, 8.025], "pivot": [0, 8, -5.5], "rotation": [0, 0, 45], "uv": [18, 120]}, {"origin": [3, 6.34315, -13], "size": [1, 3.31371, 8], "uv": [0, 120]}, {"origin": [-4, 6.34315, -13.025], "size": [1, 3.31371, 8.025], "pivot": [0, 8, -5.5], "rotation": [0, 0, 45], "uv": [112, 109]}, {"origin": [-4, 6.34315, -13], "size": [1, 3.31371, 8], "uv": [94, 109]}, {"origin": [-6, 6.34315, -23.975], "size": [1, 3.31371, 19], "uv": [40, 0]}, {"origin": [-5, 6.34315, -23.975], "size": [3.425, 3.31371, 1], "uv": [58, 146]}, {"origin": [1.575, 6.34315, -23.975], "size": [3.425, 3.31371, 1], "uv": [50, 146]}, {"origin": [5, 6.34315, -23.975], "size": [1, 3.31371, 19], "uv": [0, 0]}, {"origin": [-4, 6.34315, -43.025], "size": [1.5, 3.31371, 8.026], "pivot": [0, 8, -35.5], "rotation": [0, 0, 45], "uv": [76, 109]}, {"origin": [-4, 6.34315, -43], "size": [1.5, 3.31371, 8], "uv": [58, 109]}, {"origin": [-1.65685, 4, -43.025], "size": [3.31371, 1.5, 8.026], "pivot": [0, 8, -35.5], "rotation": [0, 0, 45], "uv": [0, 100]}, {"origin": [-1.65685, 4, -43], "size": [3.31371, 1.5, 8], "uv": [104, 87]}, {"origin": [2.5, 6.34315, -43.025], "size": [1.5, 3.31371, 8.026], "pivot": [0, 8, -35.5], "rotation": [0, 0, 45], "uv": [40, 109]}, {"origin": [2.5, 6.34315, -43], "size": [1.5, 3.31371, 8], "uv": [22, 109]}, {"origin": [-1.65685, 10.5, -43.025], "size": [3.31371, 1.5, 8.026], "pivot": [0, 8, -35.5], "rotation": [0, 0, 45], "uv": [82, 87]}, {"origin": [-1.65685, 10.25, -43], "size": [3.31371, 1.75, 8], "uv": [60, 87]}, {"origin": [-1.44975, 4.5, -23.025], "size": [2.89949, 1, 10.025], "pivot": [0, 8, -13.5], "rotation": [0, 0, 45], "uv": [48, 62]}, {"origin": [-1.44975, 4.5, -23], "size": [2.89949, 1, 10], "uv": [24, 62]}, {"origin": [-1.44975, 10.5, -23.025], "size": [2.89949, 1, 10.025], "pivot": [0, 8, -13.5], "rotation": [0, 0, 45], "uv": [0, 62]}, {"origin": [-1.44975, 10.5, -23], "size": [2.89949, 1, 10], "uv": [120, 50]}, {"origin": [2.5, 6.55025, -23.025], "size": [1, 2.89949, 10.025], "pivot": [0, 8, -13.5], "rotation": [0, 0, 45], "uv": [0, 74]}, {"origin": [2.5, 6.55025, -23], "size": [1, 2.89949, 10], "uv": [116, 62]}, {"origin": [-3.5, 6.55025, -23.025], "size": [1, 2.89949, 10.025], "pivot": [0, 8, -13.5], "rotation": [0, 0, 45], "uv": [94, 62]}, {"origin": [-3.5, 6.55025, -23], "size": [1, 2.89949, 10], "uv": [72, 62]}, {"origin": [-1.03553, 5.5, -35.025], "size": [2.07107, 1, 12.025], "pivot": [0, 8, -23.5], "rotation": [0, 0, 45], "uv": [84, 22]}, {"origin": [-1.03553, 5.5, -35], "size": [2.07107, 1, 12], "uv": [56, 22]}, {"origin": [-1.03553, 9.5, -35.025], "size": [2.07107, 1, 12.025], "pivot": [0, 8, -23.5], "rotation": [0, 0, 45], "uv": [28, 22]}, {"origin": [-1.03553, 9.5, -35], "size": [2.07107, 1, 12], "uv": [0, 22]}, {"origin": [1.5, 6.96447, -35.05], "size": [1, 2.07107, 12.05], "pivot": [0, 8, -23.5], "rotation": [0, 0, 45], "uv": [52, 36]}, {"origin": [1.5, 6.96447, -35], "size": [1, 2.07107, 12], "uv": [26, 36]}, {"origin": [-2.5, 6.96447, -35.025], "size": [1, 2.07107, 12.025], "pivot": [0, 8, -23.5], "rotation": [0, 0, 45], "uv": [0, 36]}, {"origin": [-2.5, 6.96447, -35], "size": [1, 2.07107, 12], "uv": [112, 22]}]}]}]}