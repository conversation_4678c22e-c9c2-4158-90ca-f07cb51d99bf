import { world } from '@minecraft/server';
world.afterEvents.worldLoad.subscribe(() => {
    const mutatedZombies = world.scoreboard.getObjective('mutated_zombies');
    const commandBlocksEnabled = world.gameRules.commandBlocksEnabled;
    const commandBlockOutput = world.gameRules.commandBlockOutput;
    const insomniaEnabled = world.gameRules.doInsomnia;
    if (mutatedZombies == undefined)
        world.scoreboard.addObjective('mutated_zombies').addScore('main', 0);
    if (!commandBlocksEnabled)
        world.getDimension('overworld').runCommand('gamerule commandblocksenabled true');
    if (commandBlockOutput)
        world.getDimension('overworld').runCommand('gamerule commandblockoutput false');
    if (insomniaEnabled)
        world.getDimension('overworld').runCommand('gamerule doinsomnia false');
});
