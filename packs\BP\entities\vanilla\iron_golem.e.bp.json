{"format_version": "1.21.50", "minecraft:entity": {"description": {"identifier": "minecraft:iron_golem", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"rza:transform_to_charged_iron_golem": {"minecraft:transformation": {"into": "rza:charged_iron_golem", "delay": 0}}, "minecraft:player_created": {"minecraft:behavior.hurt_by_target": {"priority": 5, "entity_types": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "operator": "!=", "value": "player"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "creeper"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "illager"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "turret"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "villager"}]}}}}, "minecraft:village_created": {"minecraft:behavior.defend_village_target": {"priority": 1, "attack_chance": 0.05, "entity_types": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"test": "is_family", "subject": "other", "value": "player"}]}}}, "minecraft:dweller": {"dwelling_type": "village", "dweller_role": "defender", "update_interval_base": 60, "update_interval_variant": 40, "can_find_poi": false, "can_migrate": true, "first_founding_reward": 0}}}, "events": {"minecraft:from_player": {"add": {"component_groups": ["minecraft:player_created"]}}, "minecraft:from_village": {"queue_command": {"command": "function world/mobs/village_summon_illagers"}}, "rza:to_charged_iron_golem": {"add": {"component_groups": ["rza:transform_to_charged_iron_golem"]}}, "rza:heal": {"queue_command": {"command": "effect @s regeneration 2 3"}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["irongolem", "mob"]}, "minecraft:nameable": {}, "minecraft:collision_box": {"width": 1.4, "height": 2.9}, "minecraft:loot": {"table": "loot_tables/entities/iron_golem.json"}, "minecraft:health": {"value": 100, "max": 100}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.25}, "minecraft:navigation.walk": {"can_path_over_water": false, "avoid_water": true, "is_amphibious": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "iron_ingot"}, {"test": "is_missing_health", "value": true}]}}, "use_item": true, "swing": true, "health_amount": 25, "play_sounds": "irongolem.repair", "interact_text": "action.interact.repair"}, {"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "tnt"}]}, "event": "rza:to_charged_iron_golem", "target": "self"}, "use_item": true, "swing": true, "play_sounds": "irongolem.repair", "interact_text": "action.interact.upgrade"}]}, "minecraft:attack": {"damage": {"range_min": 7, "range_max": 21}}, "minecraft:damage_sensor": {"triggers": [{"cause": "fall", "deals_damage": "no"}, {"cause": "block_explosion", "deals_damage": "no"}, {"cause": "entity_explosion", "deals_damage": "no"}, {"on_damage": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "turret"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}}, "deals_damage": "no"}]}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:leashable": {"soft_distance": 4, "hard_distance": 6, "max_distance": 10}, "minecraft:balloonable": {"mass": 2}, "minecraft:preferred_path": {"max_fall_blocks": 1, "jump_cost": 5, "default_block_cost": 1.5, "preferred_path_blocks": [{"cost": 0, "blocks": ["grass_path"]}, {"cost": 1, "blocks": ["cobblestone", "stone", "stonebrick", "sandstone", "mossy_cobblestone", "stone_slab", "stone_slab2", "stone_slab3", "stone_slab4", "double_stone_slab", "double_stone_slab2", "double_stone_slab3", "double_stone_slab4", "wooden_slab", "double_wooden_slab", "planks", "brick_block", "nether_brick", "red_nether_brick", "end_bricks", "red_sandstone", "stained_glass", "glass", "glowstone", "prismarine", "emerald_block", "diamond_block", "lapis_block", "gold_block", "redstone_block", "purple_glazed_terracotta", "white_glazed_terracotta", "orange_glazed_terracotta", "magenta_glazed_terracotta", "light_blue_glazed_terracotta", "yellow_glazed_terracotta", "lime_glazed_terracotta", "pink_glazed_terracotta", "gray_glazed_terracotta", "silver_glazed_terracotta", "cyan_glazed_terracotta", "blue_glazed_terracotta", "brown_glazed_terracotta", "green_glazed_terracotta", "red_glazed_terracotta", "black_glazed_terracotta"]}, {"cost": 50, "blocks": ["bed", "lectern", "composter", "grindstone", "blast_furnace", "smoker", "fletching_table", "cartography_table", "brewing_stand", "smithing_table", "cauldron", "barrel", "loom", "stonecutter"]}]}, "minecraft:behavior.melee_attack": {"priority": 1, "track_target": false}, "minecraft:behavior.move_through_village": {"priority": 3, "speed_multiplier": 0.6, "only_at_night": true}, "minecraft:behavior.move_towards_dwelling_restriction": {"priority": 4, "speed_multiplier": 1}, "minecraft:behavior.offer_flower": {"priority": 5, "filters": {"all_of": [{"test": "is_daytime", "value": true}]}}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.6, "xz_dist": 16}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 2, "must_see": true, "must_reach": true, "persist_time": 0, "reselect_targets": true, "within_radius": 12, "reevaluate_description": true, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"none_of": [{"all_of": [{"any_of": [{"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_illager"}]}, {"test": "has_mob_effect", "subject": "other", "value": "weakness"}, {"test": "has_component", "subject": "other", "value": "minecraft:transformation"}]}]}]}, "max_dist": 12}]}, "minecraft:behavior.hurt_by_target": {"priority": 5, "entity_types": {"must_see": true, "must_see_forget_duration": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}}}, "minecraft:persistent": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:follow_range": {"value": 64}, "minecraft:conditional_bandwidth_optimization": {}}}}