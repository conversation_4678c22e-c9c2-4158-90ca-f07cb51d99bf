{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:iron_golem", "materials": {"default": "iron_golem"}, "textures": {"default": "textures/entity/iron_golem/iron_golem", "cracked_high": "textures/entity/iron_golem/cracked_high", "cracked_med": "textures/entity/iron_golem/cracked_medium", "cracked_low": "textures/entity/iron_golem/cracked_low", "cracked_none": "textures/entity/iron_golem/cracked_none"}, "geometry": {"default": "geometry.iron_golem.new"}, "animations": {"walk": "animation.irongolem.walk", "idle": "animation.irongolem.idle", "has_target": "animation.irongolem.has_target", "attack": "animation.irongolem.attack", "flower": "animation.irongolem.flower", "death": "animation.irongolem.death", "look_at_target": "animation.common.look_at_target", "default_general": "controller.animation.iron_golem.general"}, "scripts": {"pre_animation": ["variable.modified_tcos0 = Math.clamp(((Math.cos(query.modified_distance_moved * 13.5) * Math.min(query.modified_move_speed, 0.6) / variable.gliding_speed_value) * 25.0), -12.5, 12.5);", "variable.attack_time = math.sin(variable.attack_time * 25);"], "animate": ["default_general", {"look_at_target": "query.is_alive"}, {"death": "!query.is_alive"}]}, "render_controllers": ["controller.render.iron_golem", "controller.render.iron_golem_cracks"]}}}