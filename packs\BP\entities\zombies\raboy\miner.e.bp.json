{
    "format_version": "1.21.50",
    "minecraft:entity": {
        "description": {
            "identifier": "rza:miner",
            "is_spawnable": true,
            "is_summonable": true,
            "is_experimental": false,
            "properties": {
                "rza:mutated": {
                    "client_sync": true,
                    "type": "bool",
                    "default": false
                }
            }
        },
        "component_groups": {
            "rza:zombie_idle": {
                "minecraft:damage_sensor": {
                    "triggers": [
                        {
                            "cause": "all",
                            "deals_damage": "yes",
                            "damage_modifier": 0,
                            "on_damage": {
                                "filters": [
                                    {
                                        "any_of": [
                                            {
                                                "test": "is_family",
                                                "subject": "other",
                                                "operator": "!=",
                                                "value": "zombie"
                                            }
                                        ]
                                    }
                                ],
                                "event": "minecraft:become_angry",
                                "target": "self"
                            }
                        },
                        {
                            "cause": "projectile",
                            "deals_damage": "yes",
                            "damage_modifier": 0,
                            "on_damage": {
                                "filters": [
                                    {
                                        "any_of": [
                                            {
                                                "test": "is_family",
                                                "subject": "other",
                                                "operator": "!=",
                                                "value": "zombie"
                                            }
                                        ]
                                    }
                                ],
                                "event": "minecraft:become_angry",
                                "target": "self"
                            }
                        },
                        {
                            "on_damage": {
                                "filters": {
                                    "any_of": [
                                        {
                                            "test": "is_family",
                                            "subject": "other",
                                            "value": "zombie"
                                        }
                                    ]
                                }
                            },
                            "deals_damage": "no"
                        }
                    ]
                }
            },
            "rza:zombie_target_acquired": {
                "minecraft:angry": {
                    "broadcast_anger": true,
                    "broadcast_range": 48,
                    "broadcast_targets": ["zombie"],
                    "duration": 8,
                    "broadcast_filters": {
                        "all_of": [
                            {
                                "test": "has_target",
                                "subject": "other",
                                "operator": "!=",
                                "value": false
                            },
                            {
                                "none_of": [
                                    {
                                        "test": "has_mob_effect",
                                        "subject": "other",
                                        "value": "weakness"
                                    }
                                ]
                            }
                        ]
                    },
                    "filters": {
                        "all_of": [
                            {
                                "test": "is_visible",
                                "subject": "other",
                                "value": true
                            },
                            {
                                "test": "is_family",
                                "subject": "other",
                                "operator": "!=",
                                "value": "zombie"
                            }
                        ]
                    },
                    "calm_event": {
                        "event": "minecraft:on_calm",
                        "target": "self"
                    }
                }
            },
            "rza:miner_regular": {
                "minecraft:attack": {
                    "damage": 4
                },
                "minecraft:behavior.delayed_attack": {
                    "priority": 3,
                    "attack_duration": 0.1,
                    "hit_delay_pct": 0.85,
                    "max_path_time": 1,
                    "random_stop_interval": 120,
                    "x_max_rotation": 30
                },
                "minecraft:equipment": {
                    "table": "loot_tables/equipment/miner/regular.lt.json"
                },
                "minecraft:health": {
                    "value": 30
                },
                "minecraft:experience_reward": {
                    "on_death": "query.last_hit_by_player?8+(query.equipment_count*math.random(1,12)):0"
                }
            },
            "rza:miner_mutated": {
                "minecraft:shooter": {
                    "def": "rza:miner_tnt"
                },
                "minecraft:behavior.ranged_attack": {
                    "speed_multiplier": 1.6,
                    "attack_interval_max": 5,
                    "attack_interval_min": 1,
                    "attack_radius": 12,
                    "swing": true
                },
                "minecraft:equipment": {
                    "table": "loot_tables/equipment/miner/tnt_thrower.lt.json"
                },
                "minecraft:health": {
                    "value": 40
                },
                "minecraft:experience_reward": {
                    "on_death": "query.last_hit_by_player?8+(query.equipment_count*math.random(5,20)):0"
                },
                "minecraft:behavior.nearest_prioritized_attackable_target": {
                    "priority": 2,
                    "must_see": true,
                    "reselect_targets": true,
                    "within_radius": 24,
                    "must_see_forget_duration": 0,
                    "entity_types": [
                        {
                            "priority": 0,
                            "filters": {
                                "test": "is_family",
                                "subject": "other",
                                "value": "turret"
                            },
                            "max_dist": 24
                        },
                        {
                            "priority": 1,
                            "filters": {
                                "all_of": [
                                    {
                                        "test": "is_family",
                                        "subject": "other",
                                        "value": "player"
                                    }
                                ]
                            },
                            "max_dist": 24
                        },
                        {
                            "priority": 1,
                            "filters": {
                                "all_of": [
                                    {
                                        "test": "is_visible",
                                        "subject": "other",
                                        "value": true
                                    },
                                    {
                                        "any_of": [
                                            {
                                                "test": "is_family",
                                                "subject": "other",
                                                "value": "snowgolem"
                                            },
                                            {
                                                "test": "is_family",
                                                "subject": "other",
                                                "value": "irongolem"
                                            }
                                        ]
                                    }
                                ]
                            },
                            "max_dist": 24
                        },
                        {
                            "priority": 2,
                            "filters": {
                                "any_of": [
                                    {
                                        "test": "is_family",
                                        "subject": "other",
                                        "value": "villager"
                                    },
                                    {
                                        "test": "is_family",
                                        "subject": "other",
                                        "value": "illager"
                                    }
                                ]
                            },
                            "max_dist": 24
                        },
                        {
                            "priority": 2,
                            "filters": {
                                "all_of": [
                                    {
                                        "test": "is_family",
                                        "subject": "other",
                                        "value": "wandering_trader"
                                    }
                                ]
                            },
                            "max_dist": 24
                        }
                    ]
                }
            }
        },
        "events": {
            "minecraft:entity_spawned": {
                "sequence": [
                    {
                        "queue_command": {
                            "command": "execute if score main mutated_zombies matches 1 run event entity @s rza:mutate"
                        }
                    },
                    {
                        "add": {
                            "component_groups": ["rza:miner_regular", "rza:zombie_idle"]
                        }
                    }
                ]
            },
            "minecraft:entity_transformed": {
                "sequence": [
                    {
                        "queue_command": {
                            "command": "execute if score main mutated_zombies matches 1 run event entity @s rza:mutate"
                        }
                    },
                    {
                        "add": {
                            "component_groups": ["rza:miner_regular", "rza:zombie_idle"]
                        }
                    }
                ]
            },
            "rza:mutate": {
                "sequence": [
                    //When not on full moon
                    {
                        "filters": {
                            "test": "moon_phase",
                            "operator": "!=",
                            "value": 0
                        },
                        "randomize": [
                            {
                                "weight": 100
                            },
                            {
                                "weight": 3,
                                "filters": {
                                    "test": "is_difficulty",
                                    "operator": "==",
                                    "value": "easy"
                                },
                                "sequence": [
                                    {
                                        "set_property": {
                                            "rza:mutated": true
                                        }
                                    },
                                    {
                                        "remove": {
                                            "component_groups": ["rza:miner_regular"]
                                        }
                                    },
                                    {
                                        "add": {
                                            "component_groups": ["rza:miner_mutated"]
                                        }
                                    }
                                ]
                            },
                            {
                                "weight": 8,
                                "filters": {
                                    "test": "is_difficulty",
                                    "operator": "==",
                                    "value": "normal"
                                },
                                "sequence": [
                                    {
                                        "set_property": {
                                            "rza:mutated": true
                                        }
                                    },
                                    {
                                        "remove": {
                                            "component_groups": ["rza:miner_regular"]
                                        }
                                    },
                                    {
                                        "add": {
                                            "component_groups": ["rza:miner_mutated"]
                                        }
                                    }
                                ]
                            },
                            {
                                "weight": 12,
                                "filters": {
                                    "test": "is_difficulty",
                                    "operator": "==",
                                    "value": "hard"
                                },
                                "sequence": [
                                    {
                                        "set_property": {
                                            "rza:mutated": true
                                        }
                                    },
                                    {
                                        "remove": {
                                            "component_groups": ["rza:miner_regular"]
                                        }
                                    },
                                    {
                                        "add": {
                                            "component_groups": ["rza:miner_mutated"]
                                        }
                                    }
                                ]
                            }
                        ]
                    },
                    //During full moon
                    {
                        "filters": {
                            "test": "moon_phase",
                            "operator": "==",
                            "value": 0
                        },
                        "randomize": [
                            {
                                "weight": 100
                            },
                            {
                                "weight": 8,
                                "filters": {
                                    "test": "is_difficulty",
                                    "operator": "==",
                                    "value": "easy"
                                },
                                "sequence": [
                                    {
                                        "set_property": {
                                            "rza:mutated": true
                                        }
                                    },
                                    {
                                        "remove": {
                                            "component_groups": ["rza:miner_regular"]
                                        }
                                    },
                                    {
                                        "add": {
                                            "component_groups": ["rza:miner_mutated"]
                                        }
                                    }
                                ]
                            },
                            {
                                "weight": 16,
                                "filters": {
                                    "test": "is_difficulty",
                                    "operator": "==",
                                    "value": "normal"
                                },
                                "sequence": [
                                    {
                                        "set_property": {
                                            "rza:mutated": true
                                        }
                                    },
                                    {
                                        "remove": {
                                            "component_groups": ["rza:miner_regular"]
                                        }
                                    },
                                    {
                                        "add": {
                                            "component_groups": ["rza:miner_mutated"]
                                        }
                                    }
                                ]
                            },
                            {
                                "weight": 30,
                                "filters": {
                                    "test": "is_difficulty",
                                    "operator": "==",
                                    "value": "hard"
                                },
                                "sequence": [
                                    {
                                        "set_property": {
                                            "rza:mutated": true
                                        }
                                    },
                                    {
                                        "remove": {
                                            "component_groups": ["rza:miner_regular"]
                                        }
                                    },
                                    {
                                        "add": {
                                            "component_groups": ["rza:miner_mutated"]
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            "minecraft:become_angry": {
                "remove": {
                    "component_groups": ["rza:zombie_idle"]
                },
                "add": {
                    "component_groups": ["rza:zombie_target_acquired"]
                }
            },
            "minecraft:on_calm": {
                "remove": {
                    "component_groups": ["rza:zombie_target_acquired"]
                },
                "add": {
                    "component_groups": ["rza:zombie_idle"]
                }
            }
        },
        "components": {
            "minecraft:is_hidden_when_invisible": {},
            "minecraft:nameable": {},
            "minecraft:type_family": {
                "family": ["zombie", "miner", "undead", "monster", "mob"]
            },
            "minecraft:equip_item": {},
            "minecraft:collision_box": {
                "width": 0.6,
                "height": 1.9
            },
            "minecraft:movement": {
                "value": 0.25
            },
            "minecraft:movement.basic": {},
            "minecraft:navigation.walk": {
                "is_amphibious": true,
                "can_pass_doors": true,
                "can_walk": true,
                "can_float": true,
                "can_path_over_water": true,
                "avoid_damage_blocks": false
            },
            "minecraft:jump.static": {},
            "minecraft:can_climb": {},
            "minecraft:behavior.float": {
                "priority": 5
            },
            "minecraft:hurt_on_condition": {
                "damage_conditions": [
                    {
                        "filters": {
                            "test": "in_lava",
                            "subject": "self",
                            "operator": "==",
                            "value": true
                        },
                        "cause": "lava",
                        "damage_per_tick": 4
                    }
                ]
            },
            "minecraft:breathable": {
                "total_supply": 15,
                "suffocate_time": 0,
                "breathes_air": true,
                "breathes_water": true
            },
            "minecraft:loot": {
                "table": "loot_tables/entities/zombies/miner.lt.json"
            },
            "minecraft:despawn": {
                "despawn_from_distance": {
                    "max_distance": 128,
                    "min_distance": 32
                }
            },
            "minecraft:behavior.equip_item": {
                "priority": 2
            },
            "minecraft:behavior.pickup_items": {
                "priority": 6,
                "max_dist": 3,
                "goal_radius": 2,
                "speed_multiplier": 1,
                "pickup_based_on_chance": true,
                "can_pickup_any_item": true,
                "excluded_items": ["minecraft:glow_ink_sac"]
            },
            "minecraft:behavior.random_stroll": {
                "priority": 7,
                "speed_multiplier": 1
            },
            "minecraft:behavior.look_at_player": {
                "priority": 8,
                "look_distance": 6,
                "probability": 0.02
            },
            "minecraft:behavior.random_look_around": {
                "priority": 9
            },
            "minecraft:behavior.hurt_by_target": {
                "priority": 1,
                "alert_same_type": true,
                "entity_types": {
                    "filters": {
                        "all_of": [
                            {
                                "test": "is_family",
                                "subject": "other",
                                "operator": "!=",
                                "value": "turret"
                            },
                            {
                                "test": "is_visible",
                                "subject": "other",
                                "value": true
                            }
                        ]
                    }
                }
            },
            "minecraft:follow_range": {
                "value": 64
            },
            "minecraft:dweller": {
                "dwelling_type": "village",
                "dweller_role": "hostile",
                "update_interval_base": 60,
                "update_interval_variant": 40,
                "can_find_poi": false,
                "can_migrate": true,
                "first_founding_reward": 0
            },
            "minecraft:behavior.move_to_village": {
                "priority": 4,
                "speed_multiplier": 1,
                "goal_radius": 2
            },
            "minecraft:behavior.nearest_prioritized_attackable_target": {
                "priority": 2,
                "must_see": true,
                "reselect_targets": true,
                "within_radius": 24,
                "must_see_forget_duration": 0,
                "entity_types": [
                    {
                        "priority": 0,
                        "filters": {
                            "all_of": [
                                {
                                    "test": "is_visible",
                                    "subject": "other",
                                    "value": true
                                },
                                {
                                    "test": "is_family",
                                    "subject": "other",
                                    "value": "player"
                                }
                            ]
                        },
                        "max_dist": 24
                    },
                    {
                        "priority": 1,
                        "filters": {
                            "all_of": [
                                {
                                    "test": "is_visible",
                                    "subject": "other",
                                    "value": true
                                },
                                {
                                    "any_of": [
                                        {
                                            "test": "is_family",
                                            "subject": "other",
                                            "value": "snowgolem"
                                        },
                                        {
                                            "test": "is_family",
                                            "subject": "other",
                                            "value": "irongolem"
                                        }
                                    ]
                                }
                            ]
                        },
                        "max_dist": 24
                    },
                    {
                        "priority": 2,
                        "filters": {
                            "all_of": [
                                {
                                    "test": "is_visible",
                                    "subject": "other",
                                    "value": true
                                },
                                {
                                    "any_of": [
                                        {
                                            "test": "is_family",
                                            "subject": "other",
                                            "value": "villager"
                                        },
                                        {
                                            "test": "is_family",
                                            "subject": "other",
                                            "value": "wandering_trader"
                                        },
                                        {
                                            "test": "is_family",
                                            "subject": "other",
                                            "value": "illager"
                                        }
                                    ]
                                }
                            ]
                        },
                        "max_dist": 24
                    }
                ]
            },
            "minecraft:physics": {},
            "minecraft:pushable": {
                "is_pushable": true,
                "is_pushable_by_piston": true
            },
            "minecraft:conditional_bandwidth_optimization": {},
            "minecraft:ambient_sound_interval": {
                "event_name": "ambient",
                "range": 10,
                "value": 7
            }
        }
    }
}
