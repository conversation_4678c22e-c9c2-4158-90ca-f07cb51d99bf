/**
 * @file Sonic Cannon functionality for the Zombie Apocalypse addon.
 * Handles sonic blast impulse mechanics for both regular and attachment variants.
 */

import { Entity, EntityDamageCause } from "@minecraft/server";
import { fixedLenRaycast } from "./raycast";
import { calculateDirection } from "../utils/vector3";

/**
 * Applies a sonic blast impulse to the target entity from a regular sonic cannon.
 * The blast creates a strong upward force combined with horizontal movement.
 * 
 * @param {Entity} entity - The target entity to receive the sonic blast
 * @param {Entity} source - The entity firing the sonic cannon (used for direction)
 * @returns {void}
 */
export function sonicCannonHit(entity: Entity, source: Entity): void {
    const cannonDir = source.getViewDirection();
    entity.applyImpulse(
        {
            x: cannonDir.x * 3, // Horizontal force multiplier
            y: cannonDir.y * 8, // Strong vertical force
            z: cannonDir.z * 3  // Horizontal force multiplier
        }
    );
    return;
}

/**
 * Fires a sonic charge blast using raycast
 * @param entity The entity firing the sonic charge
 */
export function fireSonicCharge(entity: Entity): void {
    const dimension = entity.dimension;
    const location = entity.location;
    const target = entity.target;
    
    if (!target) return;
    const targetLocation = target.location;
    
    // Calculate direction from cannon to target
    const direction = calculateDirection(location, targetLocation);
    
    const startOffset = 1.5;
    const startPos = {
        x: location.x + direction.x * startOffset,
        y: location.y + 0.55 + direction.y * startOffset,
        z: location.z + direction.z * startOffset
    };

    // Get ray positions with larger step size for sonic cannon
    const positions = fixedLenRaycast(startPos, direction, 48, 2);
    
    // Handle particles and damage for each position
    for (const pos of positions) {
        try {
            dimension.spawnParticle('rza:sonic_charge', pos);
            dimension.getEntities({ 
                location: pos, 
                families: ['zombie'], 
                maxDistance: 5 
            }).forEach(zombie => {
                zombie.applyDamage(10, { 
                    cause: EntityDamageCause.entityAttack, 
                    damagingEntity: entity 
                });
            });
        } catch (e) {}
    }
    return;
}