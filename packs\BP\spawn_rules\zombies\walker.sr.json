{
    "format_version": "1.10.0",
    "minecraft:spawn_rules": {
        "description": {
            "identifier": "rza:walker",
            "population_control": "monster"
        },
        // For daytime spawning only
        "conditions": [
            {
                "minecraft:spawns_on_surface": {},
                "minecraft:brightness_filter": {
                    "min": 9,
                    "max": 15,
                    "adjust_for_weather": false
                },
                "minecraft:difficulty_filter": {
                    "min": "easy",
                    "max": "hard"
                },
                "minecraft:world_age_filter": {
                    "min": 2400 //Day 2
                },
                "minecraft:distance_filter": {
                    "min": 32,
                    "max": 96
                },
                "minecraft:weight": {
                    "default": 80
                },
                "minecraft:herd": {
                    "min_size": 1,
                    "max_size": 5
                },
                "minecraft:density_limit": {
                    "surface": 256
                },
                "minecraft:spawns_on_block_filter": ["dirt", "stone", "grass_block"],
                "minecraft:biome_filter": {
                    "test": "has_biome_tag",
                    "operator": "==",
                    "value": "monster"
                }
            }
        ]
    }
}
