import { EntityDamageCause } from "@minecraft/server";
export function createShockwave(source, radius, maxForce, minForce, damage, targetFamilies, particleEffect) {
    const dimension = source.dimension;
    const location = source.location;
    const nearbyEntities = targetFamilies
        ? dimension.getEntities({ location, maxDistance: radius, families: targetFamilies })
        : dimension.getEntities({ location, maxDistance: radius });
    for (const target of nearbyEntities) {
        if (target.id === source.id)
            continue;
        const dx = source.location.x - target.location.x;
        const dy = source.location.y - target.location.y;
        const dz = source.location.z - target.location.z;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        const forceMagnitude = maxForce * (1 - distance / radius) + minForce;
        const dirX = target.location.x - source.location.x;
        const dirY = 0.4 + (maxForce * 0.3);
        const dirZ = target.location.z - source.location.z;
        const magnitude = Math.sqrt(dirX * dirX + dirY * dirY + dirZ * dirZ);
        const velocity = {
            x: (dirX / magnitude) * forceMagnitude,
            y: dirY * forceMagnitude,
            z: (dirZ / magnitude) * forceMagnitude
        };
        try {
            target.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: source });
            target.applyImpulse(velocity);
        }
        catch (error) {
            try {
                target.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: source });
                target.applyKnockback(dirX / magnitude, dirZ / magnitude, forceMagnitude, 0.5);
            }
            catch (fallbackError) {
                console.warn(`Failed to apply knockback to entity ${target.typeId}: ${fallbackError}`);
            }
        }
    }
    if (particleEffect) {
        dimension.spawnParticle(particleEffect, location);
    }
    return;
}
