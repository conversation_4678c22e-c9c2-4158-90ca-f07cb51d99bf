import { EntityDamageCause } from '@minecraft/server';
import { fixedLenRaycast } from './raycast';
import { calculateDirection } from '../utils/vector3';
export function fireLaserPulse(laserTurret) {
    const dimension = laserTurret.dimension;
    const location = laserTurret.location;
    const target = laserTurret.target;
    if (!target)
        return;
    const targetLocation = target.location;
    const direction = calculateDirection(location, targetLocation);
    const startOffset = 2;
    const startPos = {
        x: location.x + direction.x * startOffset,
        y: location.y + 0.65 + direction.y * startOffset,
        z: location.z + direction.z * startOffset
    };
    const positions = fixedLenRaycast(startPos, direction, 28, 0.5);
    for (const pos of positions) {
        try {
            dimension.spawnParticle('rza:purple_laser', pos);
            dimension.spawnParticle('rza:purple_laser_outward', pos);
            dimension
                .getEntities({
                location: pos,
                families: ['zombie'],
                maxDistance: 2
            })
                .forEach((zombie) => {
                zombie.applyDamage(7, {
                    cause: EntityDamageCause.contact
                });
                zombie.setOnFire(3, true);
            });
        }
        catch (e) { }
    }
    dimension.playSound('turret.laser_turret.fire', location, { volume: 8 });
    return;
}
