{"format_version": "1.21.50", "minecraft:entity": {"description": {"identifier": "rza:flamethrower_fireball", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "runtime_identifier": "minecraft:snowball"}, "component_groups": {"rza:despawn": {"minecraft:instant_despawn": {}}}, "events": {"rza:despawn": {"add": {"component_groups": ["rza:despawn"]}}}, "components": {"minecraft:collision_box": {"width": 0.1, "height": 0.1}, "minecraft:scale": {"value": 1e-05}, "minecraft:type_family": {"family": ["flamethrower_fireball", "projectile"]}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": 0, "knockback": false, "semi_random_diff_damage": false, "destroy_on_hit": false}, "stick_in_ground": {"shake_time": 0}}, "power": 1, "gravity": 0.012, "inertia": 0.95, "liquid_inertia": 0.5, "anchor": 2, "offset": [0, 0, 0], "semi_random_diff_damage": false, "uncertainty_base": 5, "uncertainty_multiplier": 3, "reflect_on_hurt": false}, "minecraft:timer": {"time": 4, "looping": true, "time_down_event": {"event": "rza:despawn", "target": "self"}}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 96, "max_dropped_ticks": 1, "use_motion_prediction_hints": true}}}}}