{
    "format_version": "1.10.0",
    "minecraft:spawn_rules": {
        "description": {
            "identifier": "minecraft:skeleton",
            "population_control": "monster"
        },
        "conditions": [
            //To walker
            {
                "minecraft:spawns_on_surface": {},
                "minecraft:spawns_on_block_prevented_filter": [
                    "minecraft:nether_wart_block",
                    "minecraft:shroomlight"
                ],
                "minecraft:brightness_filter": {
                    "min": 0,
                    "max": 7,
                    "adjust_for_weather": true
                },
                "minecraft:difficulty_filter": {
                    "min": "easy",
                    "max": "hard"
                },
                "minecraft:world_age_filter": {
                    "min": 2400 //Day 2
                },
                "minecraft:weight": {
                    "default": 100
                },
                "minecraft:herd": {
                    "min_size": 1,
                    "max_size": 2
                },
                "minecraft:spawns_on_block_filter": ["dirt", "stone", "grass_block"],
                "minecraft:biome_filter": {
                    "any_of": [
                        {
                            "all_of": [
                                {
                                    "test": "has_biome_tag",
                                    "operator": "==",
                                    "value": "monster"
                                },
                                {
                                    "test": "has_biome_tag",
                                    "operator": "!=",
                                    "value": "frozen"
                                }
                            ]
                        },
                        {
                            "test": "has_biome_tag",
                            "operator": "==",
                            "value": "soulsand_valley"
                        }
                    ]
                }
            }
        ]
    }
}
