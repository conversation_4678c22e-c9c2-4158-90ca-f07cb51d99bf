{"format_version": "1.10.0", "animations": {"animation.pillager.idle_not_charged": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.4", "bones": {"leftLeg": {"rotation": [0, 0, -5]}, "rightLeg": {"rotation": [-7.5, 0, 5], "position": [0, -0.5, 0]}, "head": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}}}, "body": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.idle_charged": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.4", "bones": {"leftLeg": {"rotation": [0, 0, -5]}, "rightLeg": {"rotation": [-7.5, 0, 5], "position": [0, -0.5, 0]}, "head": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "body": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": [10, 0, 0]}}}, "animation.pillager.idle_arms_not_charged": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.4", "bones": {"leftarm": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -5], "lerp_mode": "catmullrom"}, "0.7083": {"post": [0, 0, -5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "rightarm": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 5], "lerp_mode": "catmullrom"}, "0.7083": {"post": [0, 0, 5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.idle_arms_charged": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.4", "bones": {"leftarm": {"rotation": {"0.0": {"post": [-84.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "0.25": {"post": [-79.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "0.5": {"post": [-79.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "0.75": {"post": [-84.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "1.0": {"post": [-84.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [1, -1, -1], "lerp_mode": "catmullrom"}, "0.5": {"post": [1, -0.5, -1], "lerp_mode": "catmullrom"}, "1.0": {"post": [1, -1, -1], "lerp_mode": "catmullrom"}}}, "rightarm": {"rotation": {"0.0": {"post": [-75.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "0.25": {"post": [-70.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "0.5": {"post": [-70.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "0.75": {"post": [-75.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "1.0": {"post": [-75.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -0.5, -2], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -1, -2], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.walk_not_charged": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"leftLeg": {"rotation": {"0.0": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -1, 2], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 3, 1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}}}, "rightLeg": {"rotation": {"0.0": {"post": [19.18, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [19.18, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.79, 0.9], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -1, 2], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 3, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.79, 0.9], "lerp_mode": "catmullrom"}}}, "head": {"position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}}}, "body": {"position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.walk_charged": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"leftLeg": {"rotation": {"0.0": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -1, 2], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 3, 1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}}}, "rightLeg": {"rotation": {"0.0": {"post": [19.18, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [19.18, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.79, 0.9], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -1, 2], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 3, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.79, 0.9], "lerp_mode": "catmullrom"}}}, "head": {"position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}}}, "body": {"position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.9583": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.walk_arms_not_charged": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"leftarm": {"rotation": {"0.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [8.19, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-3.75, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-5.96, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [11.79, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}, "rightarm": {"rotation": {"0.0": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-5.96, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [11.79, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [8.19, 0, 0], "lerp_mode": "catmullrom"}, "1.1667": {"post": [-3.75, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.walk_arms_charged": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"leftarm": {"rotation": {"0.0": {"post": [-84.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "0.2083": {"post": [-79.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "0.375": {"post": [-79.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-84.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "0.75": {"post": [-84.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "0.9583": {"post": [-79.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "1.125": {"post": [-79.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-84.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}, "1.5": {"post": [-84.49428, 51.86228, -1.86718], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [1, -2, -1], "lerp_mode": "catmullrom"}, "0.375": {"post": [1, -0.5, -1], "lerp_mode": "catmullrom"}, "0.75": {"post": [1, -2, -1], "lerp_mode": "catmullrom"}, "1.125": {"post": [1, -0.5, -1], "lerp_mode": "catmullrom"}, "1.5": {"post": [1, -2, -1], "lerp_mode": "catmullrom"}}}, "rightarm": {"rotation": {"0.0": {"post": [-75.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "0.2083": {"post": [-70.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "0.375": {"post": [-70.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-75.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "0.75": {"post": [-75.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "0.9583": {"post": [-70.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "1.125": {"post": [-70.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-75.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}, "1.5": {"post": [-75.09164, -32.76963, 10.05891], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -2, -2], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, -0.5, -2], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -2, -2], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, -0.5, -2], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -2, -2], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.walk_arms_melee": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"leftarm": {"rotation": {"0.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [8.19, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-3.75, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-5.96, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [11.79, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}}}, "rightarm": {"rotation": {"0.0": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-130, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-130, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [-130, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-130, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.idle_drink": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.4", "bones": {"leftarm": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -5], "lerp_mode": "catmullrom"}, "0.7083": {"post": [0, 0, -5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "rightarm": {"rotation": [-69.4851, -36.78095, 1.29045]}}}, "animation.pillager.walk_drink": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"leftarm": {"rotation": {"0.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [8.19, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-3.75, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-5.96, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [11.79, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}, "rightarm": {"rotation": [-69.4851, -36.78095, 1.29045], "position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.riding_arms_not_charged": {"loop": true, "bones": {"leftarm": {"rotation": [-53.58411, 22.00437, -6.48664]}, "rightarm": {"rotation": [-53.58411, -22.00437, 6.48664]}}}, "animation.pillager.riding_legs": {"loop": true, "bones": {"leftLeg": {"rotation": [-63.21677, -35.09641, -3.18643]}, "rightLeg": {"rotation": [-63.21677, 35.09641, 3.18643]}}}, "animation.pillager.charge": {"loop": "hold_on_last_frame", "animation_length": 1.375, "bones": {"spine": {"rotation": {"0.0": [0, 0, 0], "0.25": [30.50409, -14.47751, -3.96713], "1.0": [30.50409, -14.47751, -3.96713], "1.375": [0, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.4167": [22.5, 0, 0], "0.6667": [22.5, 0, 0], "0.9583": [0, 0, 0]}}, "leftarm": {"rotation": {"0.0833": [0, 0, 0], "0.3333": [-45.95539, 28.87909, 25.03818], "1.0": [-6.48009, 52.21677, 81.82169], "1.2083": [-64.71206, 50.97878, 33.79179]}, "position": {"0.0833": [0, 0, 0], "0.3333": [-1, 0, -1], "1.0": [1, 0, -4], "1.2083": [0, 0, 0]}}, "rightarm": {"rotation": {"0.0": [0, 0, 0], "0.25": [-50.25916, -45.05133, 15.3853], "0.5": [-50.25916, -45.05133, 15.3853], "1.0": [-50.25916, -45.05133, 15.3853], "1.25": [-95.28671, -17.83417, 20.09274]}, "position": {"0.0": [0, 0, 0], "0.25": [0, -1, -1], "0.5": [0, -1, -1], "1.0": [0, -1, -1], "1.25": [0, 0, 0]}}}}, "animation.pillager.aim": {"loop": true, "bones": {"spine": {"rotation": [7.5, 0, 0], "position": [0, -2, 0]}, "head": {"rotation": [18.07779, -14.2906, -4.60659], "position": [0, -1, 0]}, "leftarm": {"rotation": ["-93.7538+query.target_x_rotation-this", "27.6298+math.clamp(query.target_y_rotation,-45,25)-this", -5.69569], "position": [-1, -3, 0]}, "rightarm": {"rotation": ["-97.5831+query.target_x_rotation-this", "-3.0135+math.clamp(query.target_y_rotation,-25,45)-this", -0.81691], "position": [3, -1, -4]}, "left_eyelid": {"position": [0, 0, 0]}, "right_eyelid": {"position": [0, 0, 1]}}}, "animation.pillager.celebrate": {"loop": true, "animation_length": 2, "anim_time_update": "q.anim_time+q.delta_time*1.5", "bones": {"root": {"position": {"0.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -1.44, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, -1.44, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}}}, "leftLeg": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}}}, "rightLeg": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "head": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.875": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.375": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.875": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leftarm": {"rotation": {"0.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -87.5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, -87.5], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "rightarm": {"rotation": {"0.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 87.5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 87.5], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.pillager.death": {"loop": "hold_on_last_frame", "animation_length": 2, "anim_time_update": "q.anim_time+q.delta_time*2.8", "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "1.0": [-90, 0, 0]}, "position": {"0.0": [0, 0, 0], "1.0": [0, 2, 1]}}, "rightLeg": {"rotation": {"0.0": [0, 0, 0], "0.2917": [23.30696, -9.30727, 20.57639], "0.5833": [-14.19304, -9.30727, 20.57639], "1.0": [-14.19304, -9.30727, 20.57639], "1.2083": [3.30696, -9.30727, 20.57639], "1.4167": [-1.69304, -9.30727, 20.57639], "1.625": [3.30696, -9.30727, 20.57639]}, "position": {"0.0": [0, 0, 0], "0.2917": [0, 3, -1], "0.5833": [0, 0, 0]}}, "spine": {"rotation": {"0.0": [0, 0, 0], "0.5": [32.5, 0, 0], "1.0": [32.5, 0, 0], "1.25": [0, 0, 0], "1.4583": [5, 0, 0], "1.7917": [0, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.4167": [15, 0, 0], "1.125": [15, 0, 0], "1.375": [1.29298, -37.33289, -3.82607], "1.5833": [5.4231, -41.05764, -4.40357], "1.7917": [-0.44677, -44.78238, -4.98107]}}, "leftarm": {"rotation": {"0.0": [0, 0, 0], "0.4167": [-100.08453, -7.38542, 1.30962], "1.0": [-65.43798, -22.9824, -10.11783], "1.4167": [0, 0, -47.5], "1.625": [-5, 0, -47.5], "1.875": [0, 0, -47.5]}}, "rightarm": {"rotation": {"0.0": [0, 0, 0], "0.4167": [-100.08453, 7.38542, -1.30962], "1.0": [-65.43798, 22.9824, 10.11783], "1.4167": [0, 0, 15], "1.625": [-4.33287, -2.49762, 17.5945], "1.875": [0, 0, 15]}}}}}}