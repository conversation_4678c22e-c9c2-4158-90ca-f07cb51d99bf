{"tiers": [{"total_exp_required": 0, "groups": [{"trades": [{"wants": [{"item": "minecraft:coal", "quantity": {"min": 16, "max": 24}}], "gives": [{"item": "minecraft:emerald", "quantity": 1}], "trader_exp": 1, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:charcoal", "quantity": {"min": 16, "max": 24}}], "gives": [{"item": "minecraft:emerald", "quantity": 1}], "trader_exp": 1, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:charcoal", "quantity": {"min": 8, "max": 16}}], "gives": [{"item": "minecraft:emerald", "quantity": 1}], "trader_exp": 1, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": {"min": 6, "max": 8}}], "gives": [{"item": "minecraft:iron_axe", "quantity": 1}], "trader_exp": 2, "max_uses": 6, "reward_exp": true}]}]}, {"total_exp_required": 10, "groups": [{"trades": [{"wants": [{"item": "minecraft:iron_ingot", "quantity": {"min": 7, "max": 14}}], "gives": [{"item": "minecraft:emerald", "quantity": 1}], "trader_exp": 3, "max_uses": 3, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": {"min": 9, "max": 10}}], "gives": [{"item": "minecraft:iron_sword", "functions": [{"function": "enchant_with_levels", "treasure": false, "levels": {"min": 5, "max": 19}}], "quantity": 1}], "trader_exp": 12, "max_uses": 6, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": {"min": 6, "max": 8}}], "gives": [{"item": "minecraft:iron_axe", "quantity": 1}], "trader_exp": 8, "max_uses": 6, "reward_exp": true}]}]}, {"total_exp_required": 70, "groups": [{"trades": [{"wants": [{"item": "minecraft:diamond", "quantity": {"min": 1, "max": 4}}], "gives": [{"item": "minecraft:emerald", "quantity": 1}], "trader_exp": 8, "max_uses": 12, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": {"min": 12, "max": 15}}], "gives": [{"item": "minecraft:diamond_sword", "functions": [{"function": "enchant_with_levels", "treasure": false, "levels": {"min": 5, "max": 19}}], "quantity": 1}], "trader_exp": 16, "max_uses": 4, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": {"min": 9, "max": 12}}], "gives": [{"item": "minecraft:diamond_axe", "functions": [{"function": "enchant_with_levels", "treasure": false, "levels": {"min": 5, "max": 19}}], "quantity": 1}], "trader_exp": 14, "max_uses": 4, "reward_exp": true}], "num_to_select": 0}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 10, "price_multiplier": 0.05}], "gives": [{"item": "rza:turret_base", "quantity": 1}], "trader_exp": 12, "max_uses": 4, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 26, "price_multiplier": 0.05}], "gives": [{"item": "rza:arrow_turret_item", "quantity": 1}], "trader_exp": 24, "max_uses": 2, "reward_exp": true}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 24, "price_multiplier": 0.05}], "gives": [{"item": "rza:pyro_charger_machine", "quantity": 1}], "trader_exp": 28, "max_uses": 1, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 48, "price_multiplier": 0.05}], "gives": [{"item": "rza:sonic_compressor", "quantity": 1}], "trader_exp": 32, "max_uses": 1, "reward_exp": true}]}]}, {"total_exp_required": 150, "groups": [{"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": {"min": 16, "max": 24}}], "gives": [{"item": "minecraft:netherite_sword", "functions": [{"function": "enchant_with_levels", "treasure": false, "levels": {"min": 5, "max": 19}}], "quantity": 1}], "trader_exp": 24, "max_uses": 4, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": {"min": 12, "max": 20}}], "gives": [{"item": "minecraft:netherite_axe", "functions": [{"function": "enchant_with_levels", "treasure": false, "levels": {"min": 5, "max": 19}}], "quantity": 1}], "trader_exp": 22, "max_uses": 4, "reward_exp": true}], "num_to_select": 0}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 28, "price_multiplier": 0.05}, {"item": "minecraft:blaze_rod", "quantity": 2}], "gives": [{"item": "rza:flamethrower", "quantity": 1}], "trader_exp": 24, "max_uses": 1, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 32, "price_multiplier": 0.05}, {"item": "rza:pyro_charger_machine", "quantity": 1}], "gives": [{"item": "rza:pyro_charger_item", "quantity": 1}], "trader_exp": 32, "max_uses": 1, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 52, "price_multiplier": 0.05}, {"item": "rza:sonic_compressor", "quantity": 1}], "gives": [{"item": "rza:sonic_cannon_item", "quantity": 1}], "trader_exp": 36, "max_uses": 1, "reward_exp": true}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 32, "price_multiplier": 0.05}], "gives": [{"item": "rza:electron_reactor_core", "quantity": 1}], "trader_exp": 24, "max_uses": 1, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 42, "price_multiplier": 0.05}, {"item": "rza:electron_reactor_core", "quantity": 1}], "gives": [{"item": "rza:active_electron_reactor_core", "quantity": 1}], "trader_exp": 36, "max_uses": 1, "reward_exp": true}]}]}, {"total_exp_required": 250, "groups": [{"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 64, "price_multiplier": 0.05}, {"item": "rza:electron_magnifier", "quantity": 1}], "gives": [{"item": "rza:storm_weaver_item", "quantity": 1}], "trader_exp": 48, "max_uses": 1, "reward_exp": true}]}, {"trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 24, "price_multiplier": 0.05}, {"item": "minecraft:blaze_rod", "quantity": 1}], "gives": [{"item": "rza:flamethrower", "quantity": 1}], "trader_exp": 24, "max_uses": 1, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 48, "price_multiplier": 0.05}, {"item": "rza:active_electron_reactor_core", "quantity": 1}], "gives": [{"item": "rza:electron_magnifier", "quantity": 1}], "trader_exp": 42, "max_uses": 1, "reward_exp": true}, {"wants": [{"item": "minecraft:emerald", "quantity": 42, "price_multiplier": 0.05}], "gives": [{"item": "rza:electron_channeler", "quantity": 1}], "trader_exp": 38, "max_uses": 1, "reward_exp": true}]}]}]}