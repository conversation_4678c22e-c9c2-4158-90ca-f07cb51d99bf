import { EntityDamageCause } from "@minecraft/server";
export function createShockwave(source, radius, maxForce, minForce, damage, targetFamilies, particleEffect) {
    const dimension = source.dimension;
    const location = source.location;
    const nearbyEntities = targetFamilies
        ? dimension.getEntities({ location, maxDistance: radius, families: targetFamilies })
        : dimension.getEntities({ location, maxDistance: radius });
    for (const target of nearbyEntities) {
        if (target.id === source.id)
            continue;
        const normalizedDir = {
            x: target.location.x - source.location.x,
            y: target.location.y - source.location.y,
            z: target.location.z - source.location.z
        };
        const distance = Math.sqrt(normalizedDir.x * normalizedDir.x + normalizedDir.y * normalizedDir.y + normalizedDir.z * normalizedDir.z);
        const forceMagnitude = maxForce * (1 - distance / radius) + minForce;
        const dirY = 0.4 + (maxForce * 0.3);
        const magnitude = Math.sqrt(normalizedDir.x * normalizedDir.x + dirY * dirY + normalizedDir.z * normalizedDir.z);
        const velocity = {
            x: (normalizedDir.x / magnitude) * forceMagnitude,
            y: dirY * forceMagnitude,
            z: (normalizedDir.z / magnitude) * forceMagnitude
        };
        try {
            target.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: source });
            target.applyImpulse(velocity);
        }
        catch (error) {
            try {
                const dynamicDir = {
                    x: normalizedDir.x * forceMagnitude,
                    z: normalizedDir.z * forceMagnitude
                };
                target.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: source });
                target.applyKnockback(dynamicDir, 0.5);
            }
            catch (fallbackError) {
                console.warn(`Failed to apply knockback to entity ${target.typeId}: ${fallbackError}`);
            }
        }
    }
    if (particleEffect) {
        dimension.spawnParticle(particleEffect, location);
    }
    return;
}
