import {
    Entity,
    EntityComponentTypes,
    EntityEquippableComponent,
    EntityTypeFamilyComponent,
    EquipmentSlot
} from '@minecraft/server';
import { stormWeavers, stormWeaverLightning, fireLightningStrike } from '../../turrets/stormWeaver';
import { pulsarSystemMechanics, pulsarSystems } from '../../turrets/pulsarSystem';
import { repairArrayCooldown, repairArrayMechanics } from '../../turrets/repairArray';
import {
    meleeWeaponCooldown,
    nonPlayerMeleeWeaponAttack,
    playerMeleeWeaponAttack
} from '../../weapons/melee';
import { cleanupAcidPuddleCooldown, spitterAcidPuddleEffect } from '../zombies/spitter';
import { sonicCannonHit, fireSonicCharge } from '../../turrets/sonicCannon';
import { ferralLeap } from '../zombies/feral';
import { alphaZombieMechanics } from '../zombies/alpha';
import { witheratorMechanics, witheratorSkullHit } from '../../turrets/witherator';
import { system } from '@minecraft/server';
import { pyroChargerFireball } from '../../turrets/pyroCharger';
import { handleTurretConfiguration } from '../../turrets/targetConfig';
import { flamethrowerFireball } from '../../weapons/flamethrower';
import { createShockwave } from './general';
import { fireLaserPulse } from 'rza/turrets/laserTurret';
import { transformToWalker, transformToFeral } from '../zombies/general';

/**
 * List of vanilla mobs that should be transformed to RZA zombies
 */
const vanillaMobTransformations = [
    'minecraft:creeper',
    'minecraft:enderman',
    'minecraft:skeleton',
    'minecraft:spider',
    'minecraft:stray',
    'minecraft:drowned',
    'minecraft:zombie',
    'minecraft:husk'
];

/**
 * Transforms vanilla Minecraft mobs into RZA variants
 * @param entity The entity to potentially transform
 */
export function handleVanillaMobTransformation(entity: Entity) {
    const entityType = entity.typeId;
    try {
        if (vanillaMobTransformations.includes(entityType)) {
            transformToWalker(entity, entity.dimension, entity.location);
        } else if (entityType === 'minecraft:witch') {
            transformToFeral(entity, entity.dimension, entity.location);
        }
        // Future transformations for other mob types will go here
    } catch (e) {}
    return;
}

/**
 * Cleans up entity-specific data when an entity is removed from the world
 * Handles cleanup for:
 * - Storm Weaver chain data
 * - Pulsar System timers and settings
 * - Repair Array cooldowns
 * - Melee weapon cooldowns
 * - Acid puddle effects
 *
 * @param entityType The type ID of the removed entity
 * @param entityId The unique ID of the removed entity
 */
export function handleEntityRemoveCleanup(entityType: string, entityId: string) {
    // Storm Weaver cleanup
    if (entityType === 'rza:storm_weaver') {
        stormWeavers['rza:destroyed_weavers'].delete(entityId);
        stormWeavers['rza:chain_length'].delete(entityId);
        stormWeavers['rza:chained_zombies'].delete(entityId);
    }

    // Pulsar System cleanup
    if (pulsarSystems['rza:cooldown'].has(entityId)) pulsarSystems['rza:cooldown'].delete(entityId);
    if (pulsarSystems['rza:fire_time'].has(entityId)) pulsarSystems['rza:fire_time'].delete(entityId);
    if (pulsarSystems['rza:pulse_radius_offset'].has(entityId))
        pulsarSystems['rza:pulse_radius_offset'].delete(entityId);

    // Repair Array cleanup
    if (repairArrayCooldown.has(entityId)) repairArrayCooldown.delete(entityId);

    // Melee weapon cleanup
    if (entityId == 'minecraft:pillager' || entityId == 'minecraft:vindicator') {
        meleeWeaponCooldown.delete(entityId);
    }

    // Acid puddle cleanup
    if (entityType === 'rza:spitter_acid_puddle_normal' || entityType === 'rza:spitter_acid_puddle_mutated') {
        cleanupAcidPuddleCooldown(entityId);
    }
    return;
}

/**
 * Processes entity hurt events and applies special effects based on damage source
 * Handles:
 * - Mace weapon effects
 * - Sonic Cannon impacts
 * - Storm Weaver lightning chains
 *
 * @param entity The entity that was hurt
 * @param source The entity that caused the damage
 * @param sourceId The type ID of the damage source
 * @param damage Amount of damage dealt
 * @param isZombie Whether the damaged entity is a zombie
 */
export function handleEntityHurtEvent(
    entity: Entity,
    source: Entity | undefined,
    sourceId: string | undefined,
    damage: number,
    isZombie: boolean
) {
    if (sourceId === 'minecraft:player' && source) {
        const weapon = (
            source.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent
        )?.getEquipment(EquipmentSlot.Mainhand);
        if (weapon?.type.id.endsWith('mace')) {
            playerMeleeWeaponAttack(entity, source, weapon, damage);
        }
    }

    if (sourceId === 'rza:sonic_cannon' && isZombie && source) {
        let run = system.run(() => {
            sonicCannonHit(entity, source);
            system.clearRun(run);
        });
    }

    if (sourceId === 'rza:storm_weaver' && isZombie && source) {
        let run = system.runTimeout(() => {
            stormWeaverLightning(entity, source);
            system.clearRun(run);
        }, 2);
    }
}

/**
 * Processes entity-to-entity hit events and triggers appropriate weapon effects
 * Handles:
 * - Player melee weapon attacks
 * - NPC melee weapon attacks
 * - Supercharged Iron Golem ground slams
 *
 * @param data Object containing hit event data
 */
export function handleEntityHitEntityEvent(data: { hitEntity: Entity; damagingEntity: Entity }) {
    const entity = data.hitEntity;
    const source = data.damagingEntity;
    const sourceId = source.typeId;
    const isZombie =
        source.hasComponent(EntityComponentTypes.TypeFamily) &&
        (source.getComponent(EntityComponentTypes.TypeFamily) as EntityTypeFamilyComponent).hasTypeFamily(
            'zombie'
        );

    if (!isZombie) {
        const cooldown = meleeWeaponCooldown.get(source.id);
        const isPlayer = sourceId == 'minecraft:player';
        const isNonPlayer = sourceId == 'minecraft:pillager' || sourceId == 'minecraft:vindicator';

        if (isPlayer && cooldown == 0) {
            const weapon = (
                source.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent
            ).getEquipment(EquipmentSlot.Mainhand);
            if (weapon && (weapon.type.id.endsWith('axe') || weapon.type.id.endsWith('sword'))) {
                let run = system.run(() => {
                    playerMeleeWeaponAttack(entity, source, weapon);
                    system.clearRun(run);
                });
            }
        } else if (isNonPlayer && cooldown == 0) {
            let run = system.run(() => {
                nonPlayerMeleeWeaponAttack(entity, source);
                system.clearRun(run);
            });
        } else if (sourceId == 'rza:supercharged_iron_golem') {
            createShockwave(
                source, // source
                3, // radius
                1.2, // maxForce
                0.5, // minForce
                1, // damage
                ['zombie'], // targetFamilies
                'rza:ground_slam' // particleEffect
            );
        }
    }
    return;
}

/**
 * Handles various entity trigger events in the RZA mod
 * @param data Object containing the event ID and triggering entity
 */
export function handleEntityTriggerEvents(data: { eventId: string; entity: Entity }) {
    const event = data.eventId;
    const entity = data.entity;

    switch (event) {
        case 'rza:leap':
            let leapRun = system.run(() => {
                ferralLeap(entity);
                system.clearRun(leapRun);
            });
            break;

        case 'rza:sonic_charge':
            let chargeRun = system.run(() => {
                fireSonicCharge(entity);
                system.clearRun(chargeRun);
            });
            break;

        case 'rza:laser_pulse':
            fireLaserPulse(entity);
            break;

        case 'rza:lightning_strike':
            let strikeRun = system.run(() => {
                fireLightningStrike(entity);
                system.clearRun(strikeRun);
            });
            break;

        case 'rza:explode':
            let explodeRun = system.run(() => {
                const id = entity.id;
                witheratorSkullHit(entity, id);
                system.clearRun(explodeRun);
            });
            break;

        case 'rza:alpha_zombie_buff':
            let buffRun = system.run(() => {
                alphaZombieMechanics(entity);
                system.clearRun(buffRun);
            });
            break;

        case 'rza:configure':
            const isTurret =
                entity.hasComponent(EntityComponentTypes.TypeFamily) &&
                (
                    entity.getComponent(EntityComponentTypes.TypeFamily) as EntityTypeFamilyComponent
                ).hasTypeFamily('turret');
            if (isTurret) {
                handleTurretConfiguration(entity);
            }
            break;
    }
    return;
}

/**
 * Map of entity type IDs to their corresponding handler functions
 */
const entityHandlers = new Map<string, (entity: Entity) => void>([
    ['rza:pyro_charger_fireball', pyroChargerFireball],
    ['rza:flamethrower_fireball', flamethrowerFireball],
    ['rza:pulsar_system', pulsarSystemMechanics],
    ['rza:repair_array', repairArrayMechanics],
    ['rza:witherator', witheratorMechanics],
    ['rza:spitter_acid_puddle_normal', spitterAcidPuddleEffect],
    ['rza:spitter_acid_puddle_mutated', spitterAcidPuddleEffect]
]);

/**
 * Manages ongoing mechanics and effects for special entities
 * Handles continuous updates for various entity types using a centralized handler map
 *
 * @param entity The entity to process features for
 */
export function mainEntityFeatures(entity: Entity) {
    const handler = entityHandlers.get(entity.typeId);

    if (handler) {
        const run = system.run(() => {
            handler(entity);
            system.clearRun(run);
        });
    }
    return;
}
