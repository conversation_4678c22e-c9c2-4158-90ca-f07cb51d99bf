{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "rza:left_shoulder_blood", "basic_render_parameters": {"material": "particles_blend", "texture": "textures/particle/zombies/blood"}}, "components": {"minecraft:emitter_rate_steady": {"spawn_rate": "math.random_integer(64, 96)", "max_particles": 512}, "minecraft:emitter_lifetime_once": {"active_time": 1}, "minecraft:emitter_shape_point": {"direction": ["math.random_integer(-8,8)", 24, "math.random_integer(-2,2)"]}, "minecraft:particle_initialization": {"per_update_expression": "variable.stuck_time=variable.particle_random_1*4+1;", "per_render_expression": "variable.stuck_time=variable.particle_random_1*4+1;"}, "minecraft:particle_lifetime_expression": {"max_lifetime": "math.random(10,15)"}, "minecraft:particle_initial_spin": {"rotation": "math.random(0,45)"}, "minecraft:particle_initial_speed": "math.random(0.2,3.3)", "minecraft:particle_motion_dynamic": {"linear_acceleration": [0, "variable.particle_age>0&&variable.particle_age<=0.08?math.random_integer(24,64):-10", 0]}, "minecraft:particle_appearance_billboard": {"size": ["v.particle_age<3?0.08+v.particle_age*0.01:0.11", "v.particle_age<3?0.08+v.particle_age*0.01:0.11"], "facing_camera_mode": "direction_y", "uv": {"texture_width": 16, "texture_height": 16, "uv": [0, 0], "uv_size": [16, 16]}}, "minecraft:particle_motion_collision": {"enabled": "variable.particle_age>=0.3", "collision_drag": 3, "collision_radius": 0.05}, "minecraft:particle_appearance_lighting": {}, "minecraft:particle_appearance_tinting": {"color": {"interpolant": "v.particle_age / v.particle_lifetime", "gradient": {"0.0": "#FF8C0D0D", "0.5": "#FF4F4110", "1.0": "#FF271905"}}}}}}