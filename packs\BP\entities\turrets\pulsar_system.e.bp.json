{"format_version": "1.21.50", "minecraft:entity": {"description": {"identifier": "rza:pulsar_system", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "properties": {"rza:active": {"client_sync": true, "type": "bool", "default": false}, "rza:active_state": {"client_sync": true, "type": "bool", "default": true}, "rza:convert_items_to": {"client_sync": true, "type": "enum", "default": "Charc<PERSON>l", "values": ["Charc<PERSON>l", "XP"]}, "rza:fire": {"client_sync": true, "type": "bool", "default": false}}}, "component_groups": {}, "events": {"minecraft:entity_spawned": {"sequence": [{"queue_command": {"command": "playsound random.anvil_break @a ~~~"}}]}, "rza:activate": {"set_property": {"rza:active": true}}, "rza:deactivate": {"set_property": {"rza:active": false}}, "rza:fire": {"set_property": {"rza:fire": true}}, "rza:stop_fire": {"set_property": {"rza:fire": false}}, "rza:configure": {}, "rza:despawn": {"queue_command": {"command": "function world/turrets/turret_drop/pulsar_system"}}}, "components": {"minecraft:type_family": {"family": ["turret", "pulsar_system"]}, "minecraft:collision_box": {"width": 0.7, "height": 0.8}, "minecraft:persistent": {}, "minecraft:health": {"value": 250}, "minecraft:movement": {"value": 0}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "iron_ingot"}, {"test": "is_missing_health", "value": true}]}}, "use_item": true, "swing": true, "health_amount": 25, "play_sounds": "irongolem.repair", "interact_text": "action.interact.repair"}, {"interact_text": "action.hint.turret.configure", "on_interact": {"filters": {"test": "has_equipment", "domain": "hand", "subject": "other", "operator": "!=", "value": "iron_ingot"}, "event": "rza:configure", "target": "self"}}]}, "minecraft:navigation.walk": {"is_amphibious": true, "avoid_water": true}, "minecraft:movement.basic": {}, "minecraft:damage_sensor": {"triggers": [{"cause": "entity_attack", "on_damage": {"filters": {"test": "is_family", "subject": "other", "operator": "==", "value": "player"}, "event": "rza:despawn", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "is_family", "subject": "other", "value": "turret"}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "is_family", "subject": "other", "value": "villager"}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "is_family", "subject": "other", "value": "illager"}}, "deals_damage": "no"}]}, "minecraft:follow_range": {"value": 64}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}}}}