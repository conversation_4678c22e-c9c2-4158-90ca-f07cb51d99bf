{
    "format_version": "1.10.0",
    "minecraft:spawn_rules": {
        "description": {
            "identifier": "rza:miner",
            "population_control": "monster"
        },
        "conditions": [
            {
                "minecraft:spawns_underground": {},
                "minecraft:brightness_filter": {
                    "min": 0,
                    "max": 4,
                    "adjust_for_weather": true
                },
                "minecraft:height_filter": {
                    "min": -63,
                    "max": 48
                },
                "minecraft:world_age_filter": {
                    "min": 16800 //Day 15
                },
                "minecraft:weight": {
                    "default": 10
                },
                "minecraft:herd": {
                    "min_size": 1,
                    "max_size": 2
                },
                "minecraft:density_limit": {
                    "surface": 5,
                    "underground": 5
                },
                "minecraft:biome_filter": {
                    "test": "has_biome_tag",
                    "operator": "==",
                    "value": "monster"
                }
            }
        ]
    }
}
