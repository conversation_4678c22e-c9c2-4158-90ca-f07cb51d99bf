{"format_version": "1.21.50", "minecraft:entity": {"description": {"identifier": "rza:spitter_acid_puddle_normal", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"rza:despawn": {"minecraft:instant_despawn": {}}}, "events": {"minecraft:entity_transformed": {"queue_command": {"command": ["particle rza:spitter_acid_explode_normal ~~~", "effect @a[r=5] nausea 7 0 true", "playsound mob.zombie.spitter.acid_splash @a ~~~", "playsound mob.zombie.spitter.acid_puddle @a ~~~"]}}, "rza:despawn": {"add": {"component_groups": ["rza:despawn"]}}}, "components": {"minecraft:collision_box": {"width": 0.05, "height": 0.05}, "minecraft:custom_hit_test": {"hitboxes": [{"width": 0.1, "height": 0.1, "pivot": [0, 0.05, 0]}]}, "minecraft:type_family": {"family": ["projectile", "spitter_acid"]}, "minecraft:timer": {"time": 10, "looping": true, "time_down_event": {"event": "rza:despawn", "target": "self"}}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80, "max_dropped_ticks": 0, "use_motion_prediction_hints": true}}}}}