{"format_version": "1.21.50", "minecraft:entity": {"description": {"identifier": "rza:storm_weaver_lightning", "is_spawnable": false, "is_summonable": false, "is_experimental": false, "runtime_identifier": "arrow"}, "component_groups": {"rza:despawn": {"minecraft:instant_despawn": {}}}, "events": {"minecraft:entity_spawned": {}, "rza:despawn": {"add": {"component_groups": ["rza:despawn"]}}}, "components": {"minecraft:type_family": {"family": ["projectile", "storm_weaver_lightning"]}, "minecraft:health": {"value": 4000}, "minecraft:collision_box": {"width": 0.001, "height": 0.001}, "minecraft:scale": {"value": 1e-05}, "minecraft:timer": {"time": 0.05, "looping": true, "time_down_event": {"event": "rza:despawn", "target": "self"}}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": 10, "knockback": true, "semi_random_diff_damage": false}}, "power": 0.1, "gravity": 0, "inertia": 1, "liquid_inertia": 1, "anchor": 99, "offset": [0, 0.5, 0], "semi_random_diff_damage": false, "uncertainty_base": 0, "reflect_on_hurt": false}, "minecraft:physics": {"has_collision": false}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80, "max_dropped_ticks": 0, "use_motion_prediction_hints": true}}}}}