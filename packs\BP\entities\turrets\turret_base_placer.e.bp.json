{"format_version": "1.21.50", "minecraft:entity": {"description": {"identifier": "rza:turret_base_placer", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"despawn": {"minecraft:instant_despawn": {}}}, "events": {"minecraft:entity_spawned": {"queue_command": {"command": "function world/turrets/turret_base_placer"}}, "rza:despawn": {"add": {"component_groups": ["despawn"]}}}, "components": {"minecraft:scale": {"value": 0}, "minecraft:type_family": {"family": ["dummy", "turret_base_placer"]}, "minecraft:timer": {"time": 0.1, "looping": true, "time_down_event": {"event": "rza:despawn", "target": "self"}}, "minecraft:breathable": {"total_supply": 9999, "suffocate_time": 0, "breathes_air": true, "breathes_water": true, "breathes_solids": true}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}}}}