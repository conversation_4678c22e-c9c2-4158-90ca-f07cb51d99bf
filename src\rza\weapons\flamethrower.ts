import {
  // Core types
  Player,
  Entity,
  Container,
  ItemStack,
  system,

  // Entity components
  EntityComponentTypes,
  EntityProjectileComponent,
  EntityEquippableComponent,
  EntityInventoryComponent,

  // Item components
  ItemComponentTypes,
  ItemCooldownComponent,
  ItemDurabilityComponent,

  // Equipment
  EquipmentSlot,
  Vector3
} from '@minecraft/server';

import { getRandomFloat } from '../utils/rng';

// Constants
const FLAMETHROWER_ITEM = 'rza:flamethrower';
const FIREBALL_PROJECTILE = 'rza:flamethrower_fireball' as any;
const PROJECTILE_OFFSET = 1;
const PROJECTILE_SPEED = 1.0;
const CONTINUOUS_FIRE_INTERVAL = 1; // Ticks between shots while holding
const FIRE_CHARGE_ITEM = 'minecraft:fire_charge';

// Update map definition to include durability tracking
let activeFlamethrowers = new Map<
  string,
  {
    intervalId: number;
    isUsing: boolean;
    accumulatedDamage: number;
    initialDurability: number;
  }
>();

/**
 * Gets the appropriate color code based on percentage
 * @param percentage Current percentage of fuel remaining
 * @returns Color code string
 */
function getColorForPercentage(percentage: number): string {
  if (percentage > 75) return '§a'; // Green
  if (percentage > 50) return '§e'; // Yellow
  if (percentage > 25) return '§6'; // Orange
  return '§c'; // Red
}

/**
 * Formats the durability display with dynamic colors
 * @param damage Current durability damage
 * @param max Maximum durability
 * @returns Formatted string for display
 */
function formatDurabilityText(damage: number, max: number): { actionBar: string; lore: string } {
  const remaining = Math.max(0, max - damage); // Ensure remaining never goes below 0
  const percentage = (remaining / max) * 100;
  const color = getColorForPercentage(percentage);

  return {
    actionBar: `§6Fuel: ${color}${remaining} | §7${color}${max}`,
    lore: `${color}Fuel: ${remaining} | ${max}`
  };
}

/**
 * Updates the durability display in the player's action bar and item lore
 * @param player The player to update the display for
 * @param current Current durability
 * @param max Maximum durability
 * @param itemStack Optional itemStack to update lore
 */
function updateDurabilityDisplay(player: Player, current: number, max: number, itemStack?: ItemStack) {
  const formatted = formatDurabilityText(current, max);
  player.onScreenDisplay.setActionBar(formatted.actionBar);

  if (itemStack) {
    try {
      const lore = [formatted.lore];
      itemStack.setLore(lore);
    } catch (error) {
      console.warn(`Failed to update lore for player ${player.name}: ${error}`);
    }
  }
  return;
}

/**
 * Searches player inventory for fire charges and consumes one if found
 * @param player The player to search inventory
 * @returns True if fire charge was found and consumed
 */
function consumeFireCharge(player: Player): boolean {
  try {
    const inventory = (player.getComponent(EntityComponentTypes.Inventory) as EntityInventoryComponent)
      ?.container as Container;
    if (!inventory) return false;

    for (let i = 0; i < inventory.size; i++) {
      const slot = inventory.getSlot(i);
      const item = slot.getItem();

      if (item?.typeId === FIRE_CHARGE_ITEM) {
        if (item.amount > 1) {
          slot.setItem(new ItemStack(item.type.id, item.amount - 1));
        } else {
          slot.setItem(undefined);
        }
        return true;
      }
    }
  } catch (error) {
    console.warn(`Failed to consume fire charge for player ${player.name}: ${error}`);
  }
  return false;
}

/**
 * Updates item durability and attempts refueling if possible
 * @param player The player to show the message to
 * @param itemStack The flamethrower item to update
 * @param newDamage The new durability damage value
 */
function handleOutOfFuel(player: Player, itemStack: ItemStack) {
  try {
    const durabilityComponent = itemStack.getComponent(
      ItemComponentTypes.Durability
    ) as ItemDurabilityComponent;
    if (!durabilityComponent) return;

    // Try to consume a fire charge first
    const refueled = consumeFireCharge(player);

    if (refueled) {
      // Reset durability and update display
      durabilityComponent.damage = 0;
      updateDurabilityDisplay(player, 0, durabilityComponent.maxDurability, itemStack);

      // Update the item in player's hand
      const equipmentComponent = player.getComponent(
        EntityComponentTypes.Equippable
      ) as EntityEquippableComponent;
      if (equipmentComponent) {
        equipmentComponent.setEquipment(EquipmentSlot.Mainhand, itemStack);
        player.onScreenDisplay.setActionBar('§aFlamethrower Refueled!');
        player.dimension.playSound('bucket.fill_lava', player.location, {
          volume: 0.1,
          pitch: 1.0
        });
      }
    } else {
      // No fire charges found, update to max durability and show message
      const maxDurability = durabilityComponent.maxDurability;
      durabilityComponent.damage = maxDurability;

      // Update lore to show 0 fuel before updating item
      updateDurabilityDisplay(player, maxDurability, maxDurability, itemStack);

      const equipmentComponent = player.getComponent(
        EntityComponentTypes.Equippable
      ) as EntityEquippableComponent;
      if (equipmentComponent) {
        equipmentComponent.setEquipment(EquipmentSlot.Mainhand, itemStack);
      }

      player.onScreenDisplay.setActionBar('§cOut of Fuel! Need Fire Charge!');
      player.dimension.playSound('mob.blaze.shoot', player.location, {
        volume: 0.3,
        pitch: 0.5
      });
    }
  } catch (error) {
    console.warn(`Failed to handle out of fuel for player ${player.name}: ${error}`);
  }
  return;
}

/**
 * Handles the flamethrower weapon usage
 * Spawns a fireball projectile in the direction the player is looking
 * @param itemStack The flamethrower item being used
 * @param player The player using the flamethrower
 * @param startUsing Whether this is a start or stop use action
 */
export function handleFlamethrower(itemStack: ItemStack, player: Player, startUsing?: boolean) {
  // Verify correct item
  if (itemStack.type.id !== FLAMETHROWER_ITEM) return;

  // Handle use state changes
  if (startUsing !== undefined) {
    const current = activeFlamethrowers.get(player.id);

    if (startUsing) {
      // Force cleanup any existing interval
      if (current) {
        stopFlamethrower(player);
      }

      // Start continuous firing if not already active
      const durabilityComponent = itemStack.getComponent(
        ItemComponentTypes.Durability
      ) as ItemDurabilityComponent;
      if (!durabilityComponent) return;

      // Fire initial shot immediately
      fireProjectile(itemStack, player);

      const intervalId = system.runInterval(() => {
        // Validate player and item first
        if (!player) {
          stopFlamethrower(player);
          return;
        }

        const heldItem = (
          player.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent
        )?.getEquipment(EquipmentSlot.Mainhand);
        if (!heldItem || heldItem.typeId !== FLAMETHROWER_ITEM) {
          stopFlamethrower(player);
          return;
        }

        // Check cooldown before proceeding
        const cooldownComponent = heldItem.getComponent(ItemComponentTypes.Cooldown) as ItemCooldownComponent;
        if (cooldownComponent?.getCooldownTicksRemaining(player) > 0) {
          return;
        }

        // Rest of the interval logic
        const flamethrowerState = activeFlamethrowers.get(player.id);
        if (!flamethrowerState || !flamethrowerState.isUsing) {
          stopFlamethrower(player);
          return;
        }

        // Only increment damage if projectile was successfully fired
        if (fireProjectile(itemStack, player)) {
          // Update accumulated damage and display
          if (flamethrowerState) {
            flamethrowerState.accumulatedDamage++;
            const currentDamage = Math.min(
              Math.max(flamethrowerState.initialDurability + flamethrowerState.accumulatedDamage, 0),
              durabilityComponent.maxDurability
            );

            // Update display before potentially running out of fuel
            updateDurabilityDisplay(player, currentDamage, durabilityComponent.maxDurability, itemStack);

            if (currentDamage >= durabilityComponent.maxDurability) {
              // Ensure the lore shows 0 fuel before handling out of fuel
              updateDurabilityDisplay(
                player,
                durabilityComponent.maxDurability,
                durabilityComponent.maxDurability,
                itemStack
              );
              handleOutOfFuel(player, itemStack);
              stopFlamethrower(player);
              return;
            }
          }
        }
      }, CONTINUOUS_FIRE_INTERVAL);

      // Initial display
      updateDurabilityDisplay(
        player,
        durabilityComponent.damage,
        durabilityComponent.maxDurability,
        itemStack
      );

      // Store initial state
      activeFlamethrowers.set(player.id, {
        intervalId,
        isUsing: true,
        accumulatedDamage: 0,
        initialDurability: durabilityComponent.damage
      });
    } else {
      // Stop continuous firing and apply accumulated damage
      stopFlamethrower(player);
    }
    return;
  }
  return;
}

function stopFlamethrower(player: Player) {
  const current = activeFlamethrowers.get(player.id);
  if (!current) return;

  // Clear the interval immediately
  if (current.intervalId) {
    system.clearRun(current.intervalId);
  }

  // Mark as not using
  current.isUsing = false;

  // Get equipment component
  const equipmentComponent = player.getComponent(
    EntityComponentTypes.Equippable
  ) as EntityEquippableComponent;
  if (!equipmentComponent) {
    activeFlamethrowers.delete(player.id);
    return;
  }

  // Get and validate current held item
  const heldItem = equipmentComponent.getEquipment(EquipmentSlot.Mainhand);
  if (heldItem?.typeId === FLAMETHROWER_ITEM) {
    const durabilityComponent = heldItem.getComponent(
      ItemComponentTypes.Durability
    ) as ItemDurabilityComponent;
    if (durabilityComponent) {
      // Update durability and lore with capped values
      const newDamage = Math.min(
        Math.max(current.initialDurability + current.accumulatedDamage, 0),
        durabilityComponent.maxDurability
      );
      if (newDamage >= durabilityComponent.maxDurability) {
        handleOutOfFuel(player, heldItem);
      } else {
        try {
          // Update the durability component
          durabilityComponent.damage = newDamage;

          // Update display and lore
          updateDurabilityDisplay(player, newDamage, durabilityComponent.maxDurability, heldItem);

          // Update the item in the player's hand
          equipmentComponent.setEquipment(EquipmentSlot.Mainhand, heldItem);
        } catch (error) {
          console.warn(`Failed to update flamethrower durability for player ${player.name}: ${error}`);
        }
      }
    }
  }

  // Clean up state
  activeFlamethrowers.delete(player.id);
  return;
}

/**
 * Handles firing a single projectile from the flamethrower
 * @returns boolean indicating if the projectile was successfully fired
 */
function fireProjectile(itemStack: ItemStack, player: Player): boolean {
  const dimension = player.dimension;
  const viewDirection = player.getViewDirection();
  const headLocation = player.getHeadLocation();

  // Check 3 blocks in the firing direction
  const checkPoints = [1, 2, 3].map((multiplier) => ({
    x: headLocation.x + viewDirection.x * multiplier,
    y: headLocation.y + viewDirection.y * multiplier,
    z: headLocation.z + viewDirection.z * multiplier
  }));

  // Check if all points are valid (air or passable)
  const allPointsValid = checkPoints.every((point) => {
    const block = dimension.getBlock(point);
    return block && (block.isAir || block.hasTag('passable'));
  });

  if (!allPointsValid) {
    player.onScreenDisplay.setActionBar('§cPath blocked! Cannot fire in this direction!');
    return false;
  }

  // Check if we have fuel
  const durabilityComponent = itemStack.getComponent(
    ItemComponentTypes.Durability
  ) as ItemDurabilityComponent;
  if (!durabilityComponent || durabilityComponent.damage >= durabilityComponent.maxDurability) {
    handleOutOfFuel(player, itemStack);
    return false;
  }

  // Calculate final spawn position with offset from player's head position
  const rightVector = {
    x: -viewDirection.z,
    y: 0,
    z: viewDirection.x
  };

  const spawnPos = {
    x: headLocation.x + viewDirection.x * PROJECTILE_OFFSET + rightVector.x * 0.2,
    y: headLocation.y + viewDirection.y * PROJECTILE_OFFSET,
    z: headLocation.z + viewDirection.z * PROJECTILE_OFFSET + rightVector.z * 0.2
  };

  // Spawn fireball projectile
  const fireball = dimension.spawnEntity(FIREBALL_PROJECTILE, spawnPos);

  // Get projectile component
  const projectileComponent = fireball.getComponent(
    EntityComponentTypes.Projectile
  ) as EntityProjectileComponent;

  // Calculate velocity with reduced vertical spread
  const horizontalSpreadFactor = 0.15; // Controls horizontal spread
  const verticalSpreadFactor = 0.05; // Reduced vertical spread for more consistent height
  const velocity: Vector3 = {
    x: viewDirection.x + getRandomFloat(-horizontalSpreadFactor, horizontalSpreadFactor, 3),
    y: viewDirection.y + getRandomFloat(-verticalSpreadFactor, verticalSpreadFactor, 3),
    z: viewDirection.z + getRandomFloat(-horizontalSpreadFactor, horizontalSpreadFactor, 3)
  };

  // Apply projectile velocity
  if (fireball) {
    if (projectileComponent) {
      // Apply direction and speed in a single shoot call
      projectileComponent.shoot({
        x: velocity.x * PROJECTILE_SPEED,
        y: velocity.y * PROJECTILE_SPEED,
        z: velocity.z * PROJECTILE_SPEED
      });
      dimension.playSound('mob.blaze.shoot', spawnPos, { volume: 0.1 });
    }
  }

  // Apply cooldown after successful firing
  player.startItemCooldown('flamethrower', 2);
  return true;
}

/**
 * Handles the ignition of zombies when hit by a flamethrower fireball
 * @param flamethrowerFireball - The fireball entity projectile fired by the flamethrower
 * @throws Will catch and handle any errors during entity processing
 * @returns void
 */
export function flamethrowerFireball(flamethrowerFireball: Entity): void {
  try {
    // Validate if the fireball entity exists and is still valid
    if (flamethrowerFireball && flamethrowerFireball) {
      // Search for zombies within 2 blocks of the fireball
      flamethrowerFireball.dimension
        .getEntities({
          families: ['monster'],
          location: flamethrowerFireball.location,
          maxDistance: 3
        })
        .forEach((zombie) => {
          // Set each zombie on fire for 1 minute and apply damage
          zombie.applyDamage(1);
          zombie.setOnFire(60, true);
        });
    }
  } catch (error) {
    // Log the error for debugging purposes
    console.warn(`Error in handleFlamethrowerFireball: ${error}`);
  }

  return;
}

/**
 * Cleans up all flamethrower related data for a player
 * @param playerId The ID of the player to cleanup
 */
export function cleanupFlamethrowerData(playerId: string) {
  const current = activeFlamethrowers.get(playerId);
  if (current?.intervalId) {
    system.clearRun(current.intervalId);
  }
  activeFlamethrowers.delete(playerId);
  return;
}
