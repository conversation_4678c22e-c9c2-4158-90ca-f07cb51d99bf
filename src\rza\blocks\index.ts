import { BlockComponentRegistry } from "@minecraft/server";
import { blockFeatures } from "./blocks";

/**
 * Registers all custom block components for the addon
 * @param registry The block component registry from world initialization
 */
export function blockComponents(registry: BlockComponentRegistry) {
    // Register custom components
    registry.registerCustomComponent('rza:tick', {
        onTick: (data) => {
            const block = data.block;
            const blockTypeId = block.type.id;

            blockFeatures(block, blockTypeId);
        }
    });
    return;
}
