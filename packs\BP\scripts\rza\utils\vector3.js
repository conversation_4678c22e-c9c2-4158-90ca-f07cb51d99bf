export function calculateDirection(from, to) {
    const dx = to.x - from.x;
    const dy = to.y - from.y;
    const dz = to.z - from.z;
    const magnitude = Math.sqrt(dx * dx + dy * dy + dz * dz);
    return {
        x: dx / magnitude,
        y: dy / magnitude,
        z: dz / magnitude
    };
}
export function getRandomLocation(location, dimension, baseOffset, additionalOffset, randomYOffset, checkForAirBlock) {
    const randomOffset = () => {
        const totalOffset = baseOffset + additionalOffset;
        return (Math.random() * (totalOffset * 2)) - totalOffset;
    };
    const generateRandomLocation = () => ({
        x: location.x + randomOffset(),
        y: location.y + randomYOffset,
        z: location.z + randomOffset()
    });
    const isAirBlock = (location) => {
        const block = dimension.getBlock(location);
        return block?.isAir ?? false;
    };
    let randomLocation = generateRandomLocation();
    if (!checkForAirBlock) {
        return randomLocation;
    }
    let attempts = 0;
    while (!isAirBlock(randomLocation) && attempts < 30) {
        randomLocation = generateRandomLocation();
        attempts++;
    }
    return attempts < 30 ? randomLocation : undefined;
}
export function getDistance(point1, point2) {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    const dz = point2.z - point1.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}
