{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "rza:spark", "basic_render_parameters": {"material": "particles_alpha", "texture": "textures/particle/particles"}}, "components": {"minecraft:emitter_rate_steady": {"spawn_rate": "math.random_integer(25,50)", "max_particles": 200}, "minecraft:emitter_lifetime_expression": {"activation_expression": "true"}, "minecraft:emitter_shape_point": {"direction": ["math.random_integer(-4,4)", "math.random_integer(-4,4)", "math.random_integer(-4,4)"]}, "minecraft:particle_initialization": {"per_update_expression": "variable.stuck_time=variable.particle_random_1*4+1;", "per_render_expression": "variable.stuck_time=variable.particle_random_1*4+1;"}, "minecraft:particle_lifetime_expression": {"max_lifetime": "math.random(0.1,0.5)"}, "minecraft:particle_initial_spin": {"rotation": "math.random(0,45)"}, "minecraft:particle_initial_speed": 0, "minecraft:particle_motion_dynamic": {"linear_acceleration": ["variable.particle_age<0.05?math.random_integer(-24,24):math.random_integer(-0.001,0.001)", "variable.particle_age<0.05?math.random_integer(-24,24):math.random_integer(-0.001,0.001)", "variable.particle_age<0.05?math.random_integer(-24,24):math.random_integer(-0.001,0.001)"]}, "minecraft:particle_appearance_billboard": {"size": ["v.particle_age>0&&v.particle_age<0.1?0.005+v.particle_age*0.05:0.03+v.particle_age*-0.06", "v.particle_age>0&&v.particle_age<0.1?0.005+v.particle_age*0.05:0.03+v.particle_age*-0.06"], "facing_camera_mode": "lookat_xyz", "uv": {"texture_width": 128, "texture_height": 128, "uv": [4, 4], "uv_size": [1, 1]}}, "minecraft:particle_motion_collision": {"enabled": "variable.particle_age>=0.1", "coefficient_of_restitution": 0.8, "collision_radius": 0.05}, "minecraft:particle_appearance_tinting": {"color": {"interpolant": "v.particle_age / v.particle_lifetime", "gradient": {"0.0": "#FFFFDA00", "0.39": "#FFFF8F00", "1.0": "#FFFF2D00"}}}}}}