import { EntityComponentTypes, EquipmentSlot, ItemComponentTypes, system, world } from '@minecraft/server';
import { calculateDistance, fixedPosRaycast } from './raycast';
const SKIP_REPAIR_ITEMS = ['rza:flamethrower'];
export const repairArrayCooldown = new Map();
export function repairArrayMechanics(repairArray) {
    const cooldown = repairArrayCooldown.get(repairArray.id) || 0;
    if (cooldown === 0 && repairArray.getProperty('rza:active') === true) {
        repairArrayCooldown.set(repairArray.id, 40);
        const repairArrayLocation = repairArray.location;
        const repairables = repairArray.dimension.getEntities({
            location: repairArrayLocation,
            minDistance: 1,
            maxDistance: 32
        });
        const repairableEntities = new Set();
        const equipmentSlots = [
            EquipmentSlot.Head,
            EquipmentSlot.Chest,
            EquipmentSlot.Legs,
            EquipmentSlot.Feet,
            EquipmentSlot.Mainhand,
            EquipmentSlot.Offhand
        ];
        repairables.forEach((repairable) => {
            if (!repairable.hasComponent(EntityComponentTypes.Health))
                return;
            const healthComponent = repairable.getComponent(EntityComponentTypes.Health);
            const currentHealth = healthComponent.currentValue;
            const maxHealth = healthComponent.defaultValue;
            if (repairable.typeId === 'minecraft:player') {
                const player = repairable;
                const inventory = player.getComponent(EntityComponentTypes.Inventory).container;
                let hasRepairableItems = false;
                equipmentSlots.forEach((slot) => {
                    const equipment = player?.getComponent(EntityComponentTypes.Equippable)?.getEquipment(slot);
                    if (equipment && !SKIP_REPAIR_ITEMS.includes(equipment.type.id)) {
                        const durabilityComponent = equipment?.getComponent(ItemComponentTypes.Durability);
                        if (durabilityComponent?.damage > 0) {
                            hasRepairableItems = true;
                        }
                    }
                });
                if (!hasRepairableItems) {
                    for (let i = 0; i < inventory.size; i++) {
                        const item = inventory.getItem(i);
                        if (item && !SKIP_REPAIR_ITEMS.includes(item.type.id)) {
                            const damaged = item?.getComponent(ItemComponentTypes.Durability);
                            if (damaged?.damage > 0) {
                                hasRepairableItems = true;
                                break;
                            }
                        }
                    }
                }
                if (hasRepairableItems) {
                    repairableEntities.add(repairable);
                }
            }
            else {
                if (currentHealth < maxHealth &&
                    !repairable.hasTag(`${repairArray.id}_target`) &&
                    (repairable.getComponent(EntityComponentTypes.TypeFamily).hasTypeFamily('turret') ||
                        repairable.getComponent(EntityComponentTypes.TypeFamily).hasTypeFamily('irongolem'))) {
                    repairableEntities.add(repairable);
                }
            }
        });
        const maxRepairables = Array.from(repairableEntities)
            .sort((a, b) => {
            if (a.typeId === 'minecraft:player' && b.typeId !== 'minecraft:player')
                return -1;
            if (a.typeId !== 'minecraft:player' && b.typeId === 'minecraft:player')
                return 1;
            const typeA = a.getComponent(EntityComponentTypes.TypeFamily);
            const typeB = b.getComponent(EntityComponentTypes.TypeFamily);
            const aIsGolem = typeA.hasTypeFamily('irongolem');
            const bIsGolem = typeB.hasTypeFamily('irongolem');
            if (aIsGolem && !bIsGolem)
                return 1;
            if (!aIsGolem && bIsGolem)
                return -1;
            const aIsTurret = typeA.hasTypeFamily('turret');
            const bIsTurret = typeB.hasTypeFamily('turret');
            if (aIsTurret && !bIsTurret)
                return -1;
            if (!aIsTurret && bIsTurret)
                return 1;
            const healthA = a.getComponent(EntityComponentTypes.Health)
                .currentValue;
            const healthB = b.getComponent(EntityComponentTypes.Health)
                .currentValue;
            return healthA - healthB;
        })
            .slice(0, 5);
        if (maxRepairables.length > 0) {
            repairArray.setProperty('rza:fire', true);
            repairArray.dimension.playSound('turret.repair_array.beam', repairArrayLocation, { volume: 2 });
            const delayRemoveFire = system.runTimeout(() => {
                repairArray.setProperty('rza:fire', false);
                system.clearRun(delayRemoveFire);
            }, 10);
        }
        maxRepairables.forEach((repairable) => {
            const dimension = repairable.dimension;
            const repairableLocation = repairable.location;
            repairable.addTag(`${repairArray.id}_target`);
            const repairDistanceObjective = world.scoreboard.getObjective('repair_distance') ??
                world.scoreboard.addObjective('repair_distance', 'Repair Distance');
            if (repairable.typeId === 'minecraft:player') {
                const player = repairable;
                const playerLocation = player.location;
                const inventory = player.getComponent(EntityComponentTypes.Inventory).container;
                let hasRepairableItems = false;
                equipmentSlots.forEach((slot) => {
                    const equipment = player?.getComponent(EntityComponentTypes.Equippable)?.getEquipment(slot);
                    if (equipment && SKIP_REPAIR_ITEMS.includes(equipment.type.id))
                        return;
                    const durabilityComponent = equipment?.getComponent(ItemComponentTypes.Durability);
                    if (durabilityComponent && durabilityComponent.damage > 0) {
                        durabilityComponent.damage = Math.max(durabilityComponent.damage - 2, 0);
                        (player?.getComponent(EntityComponentTypes.Equippable)).setEquipment(slot, equipment);
                        hasRepairableItems = true;
                    }
                });
                for (let i = 0; i < inventory.size; i++) {
                    const item = inventory.getItem(i);
                    if (item && SKIP_REPAIR_ITEMS.includes(item.type.id))
                        continue;
                    const damaged = item?.getComponent(ItemComponentTypes.Durability);
                    if (damaged?.damage > 0) {
                        damaged.damage = Math.max(damaged.damage - 2, 0);
                        inventory.getSlot(i).setItem(item);
                        hasRepairableItems = true;
                    }
                }
                if (hasRepairableItems) {
                    const distance = calculateDistance(repairArrayLocation, playerLocation);
                    repairDistanceObjective.setScore(repairArray, distance);
                    const beamPositions = fixedPosRaycast({
                        x: repairArrayLocation.x,
                        y: repairArrayLocation.y + 1.5,
                        z: repairArrayLocation.z
                    }, { x: playerLocation.x, y: playerLocation.y + 0.3, z: playerLocation.z }, distance, 0.5);
                    for (const pos of beamPositions) {
                        try {
                            dimension.spawnParticle('rza:repair_array_beam', pos);
                        }
                        catch (e) { }
                    }
                    player.dimension.spawnParticle('rza:repair_array_repair', playerLocation);
                    player.dimension.playSound('turret.repair_array.repair', playerLocation);
                }
            }
            else {
                const healthComponent = repairable.getComponent(EntityComponentTypes.Health);
                const health = healthComponent.currentValue;
                const maxHealth = healthComponent.defaultValue;
                const distance = calculateDistance(repairArrayLocation, repairableLocation);
                repairDistanceObjective.setScore(repairArray, distance);
                const entityBeamPositions = fixedPosRaycast({ x: repairArrayLocation.x, y: repairArrayLocation.y + 1.5, z: repairArrayLocation.z }, { x: repairableLocation.x, y: repairableLocation.y + 0.3, z: repairableLocation.z }, distance, 0.5);
                for (const pos of entityBeamPositions) {
                    try {
                        dimension.spawnParticle('rza:repair_array_beam', pos);
                    }
                    catch (e) { }
                }
                healthComponent.setCurrentValue(Math.min(health + 3, maxHealth));
                repairable.dimension.spawnParticle('rza:repair_array_repair', repairableLocation);
                repairable.dimension.playSound('turret.repair_array.repair', repairableLocation);
            }
            repairable.removeTag(`${repairArray.id}_target`);
        });
    }
    else if (cooldown > 0) {
        repairArrayCooldown.set(repairArray.id, cooldown - 1);
    }
    return;
}
