import {
    Entity,
    EntityComponentTypes,
    EntityEquippableComponent,
    EntityHealthComponent,
    EntityInventoryComponent,
    EntityTypeFamilyComponent,
    EquipmentSlot,
    ItemComponentTypes,
    ItemDurabilityComponent,
    Player,
    system,
    world
} from '@minecraft/server';
import { calculateDistance, fixedPosRaycast } from './raycast';

/**
 * Array of item identifiers that should be skipped during repair
 * This list will grow as more items are added
 */
const SKIP_REPAIR_ITEMS = ['rza:flamethrower'];

/**
 * Global Map to track cooldown timers for repair array entities
 * Used to prevent rapid-firing and control repair timing
 * @type {Map<string, number>} Map of entity IDs to their cooldown values in ticks
 */
export const repairArrayCooldown: Map<string, number> = new Map();

/**
 * Handles the repair mechanics for a repair array entity
 * Repairs both equipment and entities within range
 *
 * Features:
 * - Repairs up to 5 entities simultaneously
 * - Prioritizes players over other entities
 * - Repairs both equipped items and inventory items
 * - Visual and audio feedback through particles and sounds
 * - Uses raycast for visual beam effects
 *
 * Repair Behavior:
 * - Equipment: Reduces damage by 2 points per tick
 * - Entities: Heals 4 health points per tick
 * - Cooldown: 40 ticks between repair cycles
 * - Range: 1-32 blocks from repair array
 *
 * Entity Requirements:
 * - Must have Health component
 * - Must be either:
 *   - A player with damaged equipment/inventory
 *   - An entity with 'turret' or 'utility' type family
 *
 * @param {Entity} repairArray - The repair array entity performing repairs
 * @returns {void}
 */
export function repairArrayMechanics(repairArray: Entity): void {
    // Get current cooldown or default to 0 if not set
    const cooldown = repairArrayCooldown.get(repairArray.id) || 0;

    // Only proceed if repair array is active and not on cooldown
    if (cooldown === 0 && repairArray.getProperty('rza:active') === true) {
        // Initialize new repair cycle with 40 tick cooldown
        repairArrayCooldown.set(repairArray.id, 40);
        const repairArrayLocation = repairArray.location;

        // Query for potential repair targets within operational range
        const repairables = repairArray.dimension.getEntities({
            location: repairArrayLocation,
            minDistance: 1, // Minimum range to prevent self-targeting
            maxDistance: 32 // Maximum operational range
        });

        // Initialize collections for processing repair targets
        const repairableEntities = new Set<Entity>();
        // Define equipment slots that can be repaired
        const equipmentSlots = [
            EquipmentSlot.Head,
            EquipmentSlot.Chest,
            EquipmentSlot.Legs,
            EquipmentSlot.Feet,
            EquipmentSlot.Mainhand,
            EquipmentSlot.Offhand
        ];

        // Process each potential repair target and sort into repairable entities
        repairables.forEach((repairable) => {
            // Safety check: Skip if entity lacks health component
            if (!repairable.hasComponent(EntityComponentTypes.Health)) return;

            // Get health information for repair eligibility check
            const healthComponent = repairable.getComponent(
                EntityComponentTypes.Health
            ) as EntityHealthComponent;
            const currentHealth = healthComponent.currentValue;
            const maxHealth = healthComponent.defaultValue;

            // Handle player-specific repair logic (equipment and inventory)
            if (repairable.typeId === 'minecraft:player') {
                const player = repairable as Player;
                const inventory = (
                    player.getComponent(EntityComponentTypes.Inventory) as EntityInventoryComponent
                ).container!;
                let hasRepairableItems = false;

                // Check equipped items first
                equipmentSlots.forEach((slot) => {
                    const equipment = (
                        player?.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent
                    )?.getEquipment(slot)!;
                    if (equipment && !SKIP_REPAIR_ITEMS.includes(equipment.type.id)) {
                        const durabilityComponent = equipment?.getComponent(
                            ItemComponentTypes.Durability
                        ) as ItemDurabilityComponent;
                        if (durabilityComponent?.damage > 0) {
                            hasRepairableItems = true;
                        }
                    }
                });

                // Check inventory items if no repairable equipped items
                if (!hasRepairableItems) {
                    for (let i = 0; i < inventory.size; i++) {
                        const item = inventory.getItem(i);
                        if (item && !SKIP_REPAIR_ITEMS.includes(item.type.id)) {
                            const damaged = item?.getComponent(
                                ItemComponentTypes.Durability
                            ) as ItemDurabilityComponent;
                            if (damaged?.damage > 0) {
                                hasRepairableItems = true;
                                break;
                            }
                        }
                    }
                }

                // Only add player to repairableEntities if they have items that can be repaired
                if (hasRepairableItems) {
                    repairableEntities.add(repairable);
                }
            } else {
                // Non-player entity repair requirements:
                // 1. Must be below max health
                // 2. Not already being repaired by this array
                // 3. Must be one of: turret, utility, or iron golem
                if (
                    currentHealth < maxHealth &&
                    !repairable.hasTag(`${repairArray.id}_target`) && // Prevent double-targeting
                    ((
                        repairable.getComponent(EntityComponentTypes.TypeFamily) as EntityTypeFamilyComponent
                    ).hasTypeFamily('turret') ||
                        (
                            repairable.getComponent(
                                EntityComponentTypes.TypeFamily
                            ) as EntityTypeFamilyComponent
                        ).hasTypeFamily('irongolem'))
                ) {
                    repairableEntities.add(repairable);
                }
            }
        });

        // Sort targets by priority:
        // 1. Players (highest)
        // 2. Turrets
        // 3. Iron golems (lowest)
        // Within same priority, sort by current health (lowest first)
        const maxRepairables = Array.from(repairableEntities)
            .sort((a, b) => {
                // Player priority check
                if (a.typeId === 'minecraft:player' && b.typeId !== 'minecraft:player') return -1;
                if (a.typeId !== 'minecraft:player' && b.typeId === 'minecraft:player') return 1;

                // Get type families for priority sorting
                const typeA = a.getComponent(EntityComponentTypes.TypeFamily) as EntityTypeFamilyComponent;
                const typeB = b.getComponent(EntityComponentTypes.TypeFamily) as EntityTypeFamilyComponent;

                // Iron golems get lowest priority (positive return moves to end)
                const aIsGolem = typeA.hasTypeFamily('irongolem');
                const bIsGolem = typeB.hasTypeFamily('irongolem');
                if (aIsGolem && !bIsGolem) return 1; // Move golems towards end
                if (!aIsGolem && bIsGolem) return -1; // Move non-golems towards front

                // Turrets get second priority
                const aIsTurret = typeA.hasTypeFamily('turret');
                const bIsTurret = typeB.hasTypeFamily('turret');
                if (aIsTurret && !bIsTurret) return -1;
                if (!aIsTurret && bIsTurret) return 1;

                // For entities of same type, prioritize by health (lower health = higher priority)
                const healthA = (a.getComponent(EntityComponentTypes.Health) as EntityHealthComponent)
                    .currentValue;
                const healthB = (b.getComponent(EntityComponentTypes.Health) as EntityHealthComponent)
                    .currentValue;
                return healthA - healthB;
            })
            .slice(0, 5); // Limit to 5 targets maximum

        // Trigger repair array activation effects if targets exist
        if (maxRepairables.length > 0) {
            repairArray.setProperty('rza:fire', true);
            repairArray.dimension.playSound('turret.repair_array.beam', repairArrayLocation, { volume: 2 });
            // Reset firing state after 10 ticks
            const delayRemoveFire = system.runTimeout(() => {
                repairArray.setProperty('rza:fire', false);
                system.clearRun(delayRemoveFire);
            }, 10);
        }

        // Process repairs for each selected target
        maxRepairables.forEach((repairable) => {
            const dimension = repairable.dimension;
            const repairableLocation = repairable.location;

            // Tag the entity to mark it as being repaired
            repairable.addTag(`${repairArray.id}_target`);

            // Get or create a scoreboard objective to track repair distances
            const repairDistanceObjective =
                world.scoreboard.getObjective('repair_distance') ??
                world.scoreboard.addObjective('repair_distance', 'Repair Distance');

            // Check if the repairable entity is a player
            if (repairable.typeId === 'minecraft:player') {
                const player = repairable as Player;
                const playerLocation = player.location;
                const inventory = (
                    player.getComponent(EntityComponentTypes.Inventory) as EntityInventoryComponent
                ).container!;

                // Track if any items were actually repaired
                let hasRepairableItems = false;

                // Repair equipped items
                equipmentSlots.forEach((slot) => {
                    // Get the equipment in the current slot
                    const equipment = (
                        player?.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent
                    )?.getEquipment(slot)!;
                    // Skip items in the SKIP_REPAIR_ITEMS list
                    if (equipment && SKIP_REPAIR_ITEMS.includes(equipment.type.id)) return;
                    const durabilityComponent = equipment?.getComponent(
                        ItemComponentTypes.Durability
                    ) as ItemDurabilityComponent;

                    // If the equipment is damaged, repair it by reducing damage by 2 points
                    if (durabilityComponent && durabilityComponent.damage > 0) {
                        durabilityComponent.damage = Math.max(durabilityComponent.damage - 2, 0);
                        // Update the equipment in the slot with the repaired item
                        (
                            player?.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent
                        ).setEquipment(slot, equipment);
                        hasRepairableItems = true;
                    }
                });

                // Repair items in the player's inventory
                for (let i = 0; i < inventory.size; i++) {
                    const item = inventory.getItem(i);
                    // Skip items in the SKIP_REPAIR_ITEMS list
                    if (item && SKIP_REPAIR_ITEMS.includes(item.type.id)) continue;
                    const damaged = item?.getComponent(
                        ItemComponentTypes.Durability
                    ) as ItemDurabilityComponent;

                    // If the item is damaged, repair it by reducing damage by 2 points
                    if (damaged?.damage > 0) {
                        damaged.damage = Math.max(damaged.damage - 2, 0);
                        // Update the inventory slot with the repaired item
                        inventory.getSlot(i).setItem(item);
                        hasRepairableItems = true;
                    }
                }

                // Only proceed with effects if there were actually repairable items
                if (hasRepairableItems) {
                    // Calculate the distance between the repair array and the player
                    const distance = calculateDistance(repairArrayLocation, playerLocation);
                    // Set the repair distance score for the repair array
                    repairDistanceObjective.setScore(repairArray, distance);

                    // Create a visual beam effect from the repair array to the player
                    const beamPositions = fixedPosRaycast(
                        {
                            x: repairArrayLocation.x,
                            y: repairArrayLocation.y + 1.5,
                            z: repairArrayLocation.z
                        },
                        { x: playerLocation.x, y: playerLocation.y + 0.3, z: playerLocation.z },
                        distance,
                        0.5 // Smaller step size for repair array
                    );

                    // Spawn beam particles along the path
                    for (const pos of beamPositions) {
                        try {
                            dimension.spawnParticle('rza:repair_array_beam', pos);
                        } catch (e) {}
                    }

                    // Spawn repair particles and play repair sound at the player's location
                    player.dimension.spawnParticle('rza:repair_array_repair', playerLocation);
                    player.dimension.playSound('turret.repair_array.repair', playerLocation);
                }
            } else {
                // Handle non-player entities (e.g., turrets, utility entities, iron golems)
                const healthComponent = repairable.getComponent(
                    EntityComponentTypes.Health
                ) as EntityHealthComponent;
                const health = healthComponent.currentValue;
                const maxHealth = healthComponent.defaultValue;

                // Calculate the distance between the repair array and the entity
                const distance = calculateDistance(repairArrayLocation, repairableLocation);
                // Set the repair distance score for the repair array
                repairDistanceObjective.setScore(repairArray, distance);

                // Create a visual beam effect from the repair array to the entity
                const entityBeamPositions = fixedPosRaycast(
                    { x: repairArrayLocation.x, y: repairArrayLocation.y + 1.5, z: repairArrayLocation.z },
                    { x: repairableLocation.x, y: repairableLocation.y + 0.3, z: repairableLocation.z },
                    distance,
                    0.5 // Smaller step size for repair array
                );

                // Spawn beam particles along the path
                for (const pos of entityBeamPositions) {
                    try {
                        dimension.spawnParticle('rza:repair_array_beam', pos);
                    } catch (e) {}
                }

                // Heal the entity by 4 health points, ensuring it doesn't exceed max health
                healthComponent.setCurrentValue(Math.min(health + 3, maxHealth));

                // Spawn repair particles and play repair sound at the entity's location
                repairable.dimension.spawnParticle('rza:repair_array_repair', repairableLocation);
                repairable.dimension.playSound('turret.repair_array.repair', repairableLocation);
            }

            // Remove the repair tag from the entity
            repairable.removeTag(`${repairArray.id}_target`);
        });
    } else if (cooldown > 0) {
        // Tick down cooldown timer
        repairArrayCooldown.set(repairArray.id, cooldown - 1);
    }
    return;
}
