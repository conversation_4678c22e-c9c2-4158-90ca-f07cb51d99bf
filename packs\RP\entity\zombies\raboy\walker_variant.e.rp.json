{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "rza:walker_variant", "materials": {"default": "entity_emissive_alpha"}, "textures": {"variant1": "textures/entity/walker/player_variants/raboy_walker", "variant2": "textures/entity/walker/player_variants/sponge_with_gun"}, "geometry": {"default": "geometry.walker.normal.variant", "death": "geometry.walker.normal.variant_death"}, "animations": {"look_at_target": "animation.common.look_at_target", "idle1": "animation.walker.normal.idle1", "idle2": "animation.walker.normal.idle2", "idle3": "animation.walker.normal.idle3", "idle4": "animation.walker.normal.idle4", "idle5": "animation.walker.normal.idle5", "walk1": "animation.walker.normal.walk1", "walk2": "animation.walker.normal.walk2", "walk3": "animation.walker.normal.walk3", "walk4": "animation.walker.normal.walk4", "walk5": "animation.walker.normal.walk5", "attack1": "animation.walker.normal.attack1", "attack2": "animation.walker.normal.attack2", "attack3": "animation.walker.normal.attack3", "death1": "animation.walker.normal.death1", "death2": "animation.walker.normal.death2", "death3": "animation.walker.normal.death3", "death4": "animation.walker.normal.death4", "death_rot": "animation.general.death_rot", "general": "controller.animation.walker.normal.general", "attack_controller": "controller.animation.walker.normal.attack", "death_controller": "controller.animation.walker.normal.death"}, "scripts": {"initialize": ["variable.variants = math.random_integer(0, 30);"], "animate": ["death_controller"]}, "particle_effects": {"blood": "rza:blood"}, "render_controllers": ["controller.render.walker_variant"], "spawn_egg": {"base_color": "#462638", "overlay_color": "#965959"}}}}