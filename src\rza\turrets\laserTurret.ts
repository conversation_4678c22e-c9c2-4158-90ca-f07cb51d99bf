import { Entity, EntityDamageCause } from '@minecraft/server';
import { fixedLenRaycast } from './raycast';
import { calculateDirection } from '../utils/vector3';

/**
 * Fires a laser pulse using raycast
 * @param laserTurret The laserTurret firing the laser pulse
 * @param target The target entity to fire at
 */
export function fireLaserPulse(laserTurret: Entity): void {
    const dimension = laserTurret.dimension;
    const location = laserTurret.location;
    const target = laserTurret.target;

    if (!target) return;
    const targetLocation = target.location;

    // Calculate direction from turret to target
    const direction = calculateDirection(location, targetLocation);

    const startOffset = 2;
    const startPos = {
        x: location.x + direction.x * startOffset,
        y: location.y + 0.65 + direction.y * startOffset,
        z: location.z + direction.z * startOffset
    };

    // Get ray positions with smaller step size for laser turret
    const positions = fixedLenRaycast(startPos, direction, 28, 0.5);

    // Handle particles and damage for each position
    for (const pos of positions) {
        try {
            dimension.spawnParticle('rza:purple_laser', pos);
            dimension.spawnParticle('rza:purple_laser_outward', pos);
            dimension
                .getEntities({
                    location: pos,
                    families: ['zombie'],
                    maxDistance: 2
                })
                .forEach((zombie) => {
                    zombie.applyDamage(7, {
                        cause: EntityDamageCause.contact
                    });
                    zombie.setOnFire(3, true);
                });
        } catch (e) {}
    }

    dimension.playSound('turret.laser_turret.fire', location, { volume: 8 });
    return;
}
