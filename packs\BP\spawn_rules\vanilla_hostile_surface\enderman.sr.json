{
    "format_version": "1.10.0",
    "minecraft:spawn_rules": {
        "description": {
            "identifier": "minecraft:enderman",
            "population_control": "monster"
        },
        "conditions": [
            //To walker
            {
                "minecraft:spawns_on_surface": {},
                "minecraft:brightness_filter": {
                    "min": 0,
                    "max": 7,
                    "adjust_for_weather": true
                },
                "minecraft:difficulty_filter": {
                    "min": "easy",
                    "max": "hard"
                },
                "minecraft:world_age_filter": {
                    "min": 2400 //Day 2
                },
                "minecraft:weight": {
                    "default": 100
                },
                "minecraft:herd": {
                    "min_size": 1,
                    "max_size": 2
                },
                "minecraft:spawns_on_block_filter": ["dirt", "stone", "grass_block"],
                "minecraft:biome_filter": {
                    "test": "has_biome_tag",
                    "operator": "==",
                    "value": "monster"
                }
            },
            {
                "minecraft:spawns_underground": {},
                "minecraft:brightness_filter": {
                    "min": 0,
                    "max": 7,
                    "adjust_for_weather": true
                },
                "minecraft:difficulty_filter": {
                    "min": "easy",
                    "max": "hard"
                },
                "minecraft:weight": {
                    "default": 6
                },
                "minecraft:herd": {
                    "min_size": 1,
                    "max_size": 8
                },
                "minecraft:biome_filter": {
                    "all_of": [
                        {
                            "test": "has_biome_tag",
                            "operator": "==",
                            "value": "nether"
                        },
                        {
                            "test": "has_biome_tag",
                            "operator": "==",
                            "value": "spawn_endermen"
                        }
                    ]
                }
            },
            {
                "minecraft:spawns_underground": {},
                "minecraft:difficulty_filter": {
                    "min": "easy",
                    "max": "hard"
                },
                "minecraft:weight": {
                    "default": 10
                },
                "minecraft:herd": {
                    "min_size": 1,
                    "max_size": 4
                },
                "minecraft:biome_filter": {
                    "test": "has_biome_tag",
                    "operator": "==",
                    "value": "warped_forest"
                }
            },
            {
                "minecraft:spawns_on_surface": {},
                "minecraft:weight": {
                    "default": 10
                },
                "minecraft:herd": {
                    "min_size": 4,
                    "max_size": 4
                },
                "minecraft:biome_filter": [
                    {
                        "test": "has_biome_tag",
                        "operator": "==",
                        "value": "the_end"
                    }
                ]
            }
        ]
    }
}
